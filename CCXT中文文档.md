# CCXT – 加密货币交易所交易库

[![NPM Downloads](https://img.shields.io/npm/dy/ccxt.svg)](https://www.npmjs.com/package/ccxt) [![npm](https://img.shields.io/npm/v/ccxt.svg)](https://npmjs.com/package/ccxt) [![PyPI](https://img.shields.io/pypi/v/ccxt.svg)](https://pypi.python.org/pypi/ccxt) [![NuGet version](https://img.shields.io/nuget/v/ccxt)](https://www.nuget.org/packages/ccxt) [![GoDoc](https://pkg.go.dev/badge/github.com/ccxt/ccxt/go/v4?utm_source=godoc)](https://godoc.org/github.com/ccxt/ccxt/go/v4) [![Discord](https://img.shields.io/discord/690203284119617602?logo=discord&logoColor=white)](https://discord.gg/ccxt) [![支持的交易所](https://img.shields.io/badge/exchanges-106-blue.svg)](https://github.com/ccxt/ccxt/wiki/Exchange-Markets)

一个支持多种编程语言的加密货币交易和电子商务库，包括 `JavaScript`、`Python`、`PHP`、`C#`、`Go`，支持众多比特币/以太坊/山寨币交易所市场和商户API。

## 目录

- [安装](#安装)
- [使用方法](#使用方法)
- [支持的交易所](#支持的交易所)
- [功能特性](#功能特性)
- [快速开始](#快速开始)
- [API文档](#api文档)
- [常见问题](#常见问题)

## 概述

**CCXT** 库用于连接和交易全球的加密货币交易所和支付处理服务。它提供对市场数据的快速访问，用于存储、分析、可视化、指标开发、算法交易、策略回测、机器人编程和相关软件工程。

它专为**程序员、开发者、技术熟练的交易者、数据科学家和金融分析师**构建交易算法而设计。

### 当前功能列表

- 支持众多加密货币交易所 — 更多即将推出
- 完全实现的公共和私有API
- 可选的标准化数据，用于跨交易所分析和套利
- 开箱即用的统一API，极易集成
- 支持 Node 10.4+、Python 3、PHP 8.1+、netstandard2.0/2.1、Go 1.20+ 和网页浏览器

## 安装

安装CCXT库最简单的方法是使用包管理器：

### JavaScript (NPM)

```bash
npm install ccxt
```

```javascript
// CommonJS
var ccxt = require('ccxt')
console.log(ccxt.exchanges) // 打印所有可用交易所

// ES模块
import {version, exchanges} from 'ccxt';
console.log(version, Object.keys(exchanges));
```

### Python

```bash
pip install ccxt
```

```python
import ccxt
print(ccxt.exchanges)  # 打印所有可用交易所类的列表
```

异步支持（Python 3.7.0+）：

```python
import ccxt.async_support as ccxt  # 链接到ccxt的异步版本
```

### PHP

```bash
composer require ccxt/ccxt
```

```php
include "ccxt.php";
var_dump(\ccxt\Exchange::$exchanges); // 打印所有可用交易所类的列表
```

### C#

```bash
dotnet add package ccxt
```

```csharp
using ccxt;
Console.WriteLine(ccxt.Exchanges) // 稍后检查
```

### Go

```bash
go install github.com/ccxt/ccxt/go/v4@latest
```

```go
import "ccxt"
fmt.Println(ccxt.Exchanges)
```

## 支持的交易所

CCXT库目前支持以下106个加密货币交易所市场和交易API：

### 认证交易所

| 交易所 | ID | 名称 | 版本 | 类型 | 认证 | Pro版本 | 折扣 |
|--------|----|----- |------|------|------|---------|------|
| ![binance](https://github.com/user-attachments/assets/e9419b93-ccb0-46aa-9bff-c883f096274b) | binance | [Binance](https://accounts.binance.com/en/register?ref=D7YA7CLY) | * | CEX | ✅ | ✅ | 10% |
| ![bybit](https://github.com/user-attachments/assets/97a5d0b3-de10-423d-90e1-6620960025ed) | bybit | [Bybit](https://www.bybit.com/register?affiliate_id=35953) | 5 | CEX | ✅ | ✅ | - |
| ![okx](https://user-images.githubusercontent.com/1294454/*********-38b19e4a-bece-4dec-979a-5982859ffc04.jpg) | okx | [OKX](https://www.okx.com/join/CCXT2023) | 5 | CEX | ✅ | ✅ | 20% |

*更多交易所请参考完整列表...*

## 功能特性

### 公共API（无需API密钥）

- 市场数据
- 交易对/工具
- 价格信息（汇率）
- 订单簿
- 交易历史
- 行情数据
- OHLC(V) K线图数据
- 其他公共端点

### 私有API（需要API密钥）

- 管理个人账户信息
- 查询账户余额
- 通过市价单和限价单进行交易
- 存取法币和加密货币资金
- 查询个人订单
- 获取账本历史
- 在账户间转移资金
- 使用商户服务

## 快速开始

### JavaScript 示例

```javascript
'use strict';
const ccxt = require('ccxt');

(async function () {
    let exchange = new ccxt.binance({
        'apiKey': 'YOUR_API_KEY',
        'secret': 'YOUR_SECRET',
    });

    // 获取市场数据
    console.log(await exchange.loadMarkets());
    
    // 获取订单簿
    console.log(await exchange.fetchOrderBook('BTC/USDT'));
    
    // 获取行情
    console.log(await exchange.fetchTicker('BTC/USDT'));
    
    // 获取账户余额
    console.log(await exchange.fetchBalance());
    
    // 下单（市价卖单）
    console.log(await exchange.createMarketSellOrder('BTC/USDT', 0.001));
    
    // 下单（限价买单）
    console.log(await exchange.createLimitBuyOrder('BTC/USDT', 0.001, 50000));
})();
```

### Python 示例

```python
import ccxt

# 初始化交易所
exchange = ccxt.binance({
    'apiKey': 'YOUR_API_KEY',
    'secret': 'YOUR_SECRET',
})

# 加载市场
markets = exchange.load_markets()
print(exchange.id, markets)

# 获取订单簿
orderbook = exchange.fetch_order_book('BTC/USDT')
print(orderbook)

# 获取行情
ticker = exchange.fetch_ticker('BTC/USDT')
print(ticker)

# 获取余额
balance = exchange.fetch_balance()
print(balance)

# 市价卖单
order = exchange.create_market_sell_order('BTC/USDT', 0.001)
print(order)

# 限价买单
order = exchange.create_limit_buy_order('BTC/USDT', 0.001, 50000)
print(order)
```

## API方法

### 市场数据方法

- `loadMarkets()` - 加载所有可用市场
- `fetchTicker(symbol)` - 获取单个交易对的行情
- `fetchTickers([symbols])` - 获取多个交易对的行情
- `fetchOrderBook(symbol)` - 获取订单簿
- `fetchTrades(symbol)` - 获取最近交易
- `fetchOHLCV(symbol, timeframe)` - 获取K线数据

### 交易方法

- `fetchBalance()` - 获取账户余额
- `createOrder(symbol, type, side, amount, price, params)` - 创建订单
- `createMarketBuyOrder(symbol, amount)` - 创建市价买单
- `createMarketSellOrder(symbol, amount)` - 创建市价卖单
- `createLimitBuyOrder(symbol, amount, price)` - 创建限价买单
- `createLimitSellOrder(symbol, amount, price)` - 创建限价卖单
- `cancelOrder(id, symbol)` - 取消订单
- `fetchOrder(id, symbol)` - 获取订单信息
- `fetchOrders(symbol)` - 获取所有订单
- `fetchOpenOrders(symbol)` - 获取未完成订单
- `fetchClosedOrders(symbol)` - 获取已完成订单

### 账户方法

- `fetchMyTrades(symbol)` - 获取个人交易历史
- `fetchDeposits()` - 获取充值记录
- `fetchWithdrawals()` - 获取提现记录
- `withdraw(currency, amount, address, tag, params)` - 提现

## 错误处理

CCXT定义了一套标准的异常类型：

```python
try:
    ticker = exchange.fetch_ticker('BTC/USDT')
except ccxt.NetworkError as e:
    print('网络错误:', e)
except ccxt.ExchangeError as e:
    print('交易所错误:', e)
except Exception as e:
    print('其他错误:', e)
```

## 配置选项

```javascript
let exchange = new ccxt.binance({
    'apiKey': 'your_api_key',
    'secret': 'your_secret',
    'timeout': 30000,        // 请求超时时间（毫秒）
    'rateLimit': 1200,       // 请求频率限制（毫秒）
    'verbose': true,         // 详细日志
    'sandbox': true,         // 使用测试环境
})
```

## 常见问题

### 1. 如何获取API密钥？

访问相应交易所的官方网站，注册账户后在API管理页面创建API密钥。

### 2. 支持哪些订单类型？

- `market` - 市价单
- `limit` - 限价单
- `stop` - 止损单（部分交易所支持）
- `stop_limit` - 止损限价单（部分交易所支持）

### 3. 如何处理频率限制？

CCXT内置了频率限制处理，会自动等待适当的时间间隔。你也可以通过`rateLimit`参数调整。

### 4. 支持WebSocket吗？

是的，CCXT Pro版本支持WebSocket实时数据流。

## 许可证

CCXT采用MIT许可证，这意味着任何开发者都可以免费用于商业和开源软件开发，但使用时需自担风险，不提供任何保证。

## 联系我们

- 商务咨询：<EMAIL>
- GitHub：https://github.com/ccxt/ccxt
- Discord：https://discord.gg/ccxt
- Twitter：https://twitter.com/ccxt_official

## 免责声明

CCXT不是服务或服务器，而是一个软件。CCXT是MIT许可证下的免费开源非托管API代理软件。

- **非托管**意味着CCXT不是交易中介，在任何时候都不持有交易者的资金
- **MIT许可证**意味着CCXT可用于任何目的，但使用时需自担风险，不提供任何保证
- **免费软件**意味着CCXT免费使用，没有隐藏费用

## 高级用法

### 统一API vs 交易所特定API

CCXT提供两种API访问方式：

#### 统一API（推荐）
```javascript
// 统一的方法名，适用于所有交易所
const ticker = await exchange.fetchTicker('BTC/USDT');
const balance = await exchange.fetchBalance();
```

#### 交易所特定API
```javascript
// 直接调用交易所原生API
const response = await exchange.publicGetTicker24hr({
    'symbol': 'BTCUSDT'
});
```

### 分页和限制

许多API方法支持分页参数：

```python
# 获取最近100笔交易
trades = exchange.fetch_trades('BTC/USDT', limit=100)

# 获取指定时间后的交易
since = exchange.milliseconds() - 86400000  # 24小时前
trades = exchange.fetch_trades('BTC/USDT', since=since)

# 分页获取订单
orders = exchange.fetch_orders('BTC/USDT', since=since, limit=50)
```

### 自定义参数

可以向API方法传递交易所特定的参数：

```javascript
// Binance特定参数
const order = await exchange.createOrder('BTC/USDT', 'limit', 'buy', 1, 50000, {
    'timeInForce': 'GTC',  // Good Till Cancelled
    'recvWindow': 5000
});

// OKX特定参数
const order = await exchange.createOrder('BTC/USDT', 'limit', 'buy', 1, 50000, {
    'tdMode': 'cash',      // 交易模式
    'ccy': 'USDT'         // 保证金币种
});
```

## 数据结构

### 行情数据结构

```javascript
{
    'symbol': 'BTC/USDT',
    'timestamp': 1640995200000,
    'datetime': '2022-01-01T00:00:00.000Z',
    'high': 47800.0,
    'low': 46200.0,
    'bid': 47000.0,
    'bidVolume': 1.5,
    'ask': 47100.0,
    'askVolume': 2.0,
    'vwap': 47000.0,
    'open': 46500.0,
    'close': 47000.0,
    'last': 47000.0,
    'previousClose': 46500.0,
    'change': 500.0,
    'percentage': 1.075,
    'average': 46750.0,
    'baseVolume': 1000.0,
    'quoteVolume': 47000000.0,
    'info': {...}  // 原始响应数据
}
```

### 订单数据结构

```javascript
{
    'id': '12345',
    'clientOrderId': 'myOrder123',
    'timestamp': 1640995200000,
    'datetime': '2022-01-01T00:00:00.000Z',
    'lastTradeTimestamp': 1640995300000,
    'symbol': 'BTC/USDT',
    'type': 'limit',
    'side': 'buy',
    'amount': 1.0,
    'price': 47000.0,
    'cost': 47000.0,
    'average': 47000.0,
    'filled': 1.0,
    'remaining': 0.0,
    'status': 'closed',
    'fee': {
        'currency': 'USDT',
        'cost': 47.0,
        'rate': 0.001
    },
    'trades': [...],
    'info': {...}
}
```

### 余额数据结构

```javascript
{
    'info': {...},  // 原始响应
    'BTC': {
        'free': 1.0,      // 可用余额
        'used': 0.5,      // 冻结余额
        'total': 1.5      // 总余额
    },
    'USDT': {
        'free': 10000.0,
        'used': 5000.0,
        'total': 15000.0
    },
    'free': {
        'BTC': 1.0,
        'USDT': 10000.0
    },
    'used': {
        'BTC': 0.5,
        'USDT': 5000.0
    },
    'total': {
        'BTC': 1.5,
        'USDT': 15000.0
    }
}
```

## WebSocket支持（CCXT Pro）

CCXT Pro提供WebSocket实时数据流支持：

### 安装CCXT Pro

```bash
npm install ccxt.pro
```

### 实时行情数据

```javascript
const ccxtpro = require('ccxt.pro');

const exchange = new ccxtpro.binance();

// 订阅实时行情
const ticker = await exchange.watchTicker('BTC/USDT');
console.log(ticker);

// 订阅实时订单簿
const orderbook = await exchange.watchOrderBook('BTC/USDT');
console.log(orderbook);

// 订阅实时交易
const trades = await exchange.watchTrades('BTC/USDT');
console.log(trades);
```

### 实时账户数据

```javascript
// 订阅余额变化
const balance = await exchange.watchBalance();
console.log(balance);

// 订阅订单状态变化
const orders = await exchange.watchOrders();
console.log(orders);

// 订阅个人交易
const myTrades = await exchange.watchMyTrades();
console.log(myTrades);
```

## 测试环境

大多数交易所都提供测试环境（沙盒）：

```javascript
const exchange = new ccxt.binance({
    'apiKey': 'testnet_api_key',
    'secret': 'testnet_secret',
    'sandbox': true,  // 启用测试环境
});
```

## 代理设置

如果需要通过代理访问：

```javascript
const exchange = new ccxt.binance({
    'proxy': 'http://proxy.example.com:8080',
    // 或者使用SOCKS代理
    'proxy': 'socks://proxy.example.com:1080',
});
```

## 多线程/异步处理

### Python异步示例

```python
import asyncio
import ccxt.async_support as ccxt

async def main():
    exchange = ccxt.binance()

    # 并发获取多个交易对的行情
    symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']
    tasks = [exchange.fetch_ticker(symbol) for symbol in symbols]
    tickers = await asyncio.gather(*tasks)

    for ticker in tickers:
        print(f"{ticker['symbol']}: {ticker['last']}")

    await exchange.close()

asyncio.run(main())
```

### JavaScript并发示例

```javascript
const exchange = new ccxt.binance();

async function fetchMultipleTickers() {
    const symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT'];

    const promises = symbols.map(symbol =>
        exchange.fetchTicker(symbol)
    );

    const tickers = await Promise.all(promises);

    tickers.forEach(ticker => {
        console.log(`${ticker.symbol}: ${ticker.last}`);
    });
}

fetchMultipleTickers();
```

## 错误处理最佳实践

```python
import ccxt
import time

def safe_fetch_ticker(exchange, symbol, max_retries=3):
    for attempt in range(max_retries):
        try:
            return exchange.fetch_ticker(symbol)
        except ccxt.NetworkError as e:
            print(f'网络错误 (尝试 {attempt + 1}/{max_retries}): {e}')
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
        except ccxt.ExchangeError as e:
            print(f'交易所错误: {e}')
            break
        except ccxt.RateLimitExceeded as e:
            print(f'频率限制: {e}')
            time.sleep(exchange.rateLimit / 1000)

    return None

# 使用示例
exchange = ccxt.binance()
ticker = safe_fetch_ticker(exchange, 'BTC/USDT')
if ticker:
    print(f"BTC/USDT价格: {ticker['last']}")
```

## 性能优化建议

### 1. 复用交易所实例

```javascript
// 好的做法
const exchange = new ccxt.binance();
const ticker1 = await exchange.fetchTicker('BTC/USDT');
const ticker2 = await exchange.fetchTicker('ETH/USDT');

// 避免这样做
const ticker1 = await (new ccxt.binance()).fetchTicker('BTC/USDT');
const ticker2 = await (new ccxt.binance()).fetchTicker('ETH/USDT');
```

### 2. 批量获取数据

```javascript
// 好的做法 - 一次获取多个行情
const tickers = await exchange.fetchTickers(['BTC/USDT', 'ETH/USDT']);

// 避免多次单独请求
const btcTicker = await exchange.fetchTicker('BTC/USDT');
const ethTicker = await exchange.fetchTicker('ETH/USDT');
```

### 3. 合理设置频率限制

```javascript
const exchange = new ccxt.binance({
    'rateLimit': 1200,  // 根据交易所限制调整
    'enableRateLimit': true,  // 启用自动频率限制
});
```

## 常用工具函数

### 价格精度处理

```javascript
// 获取交易对的价格精度
const market = exchange.market('BTC/USDT');
const pricePrecision = market.precision.price;
const amountPrecision = market.precision.amount;

// 格式化价格
const price = exchange.priceToPrecision('BTC/USDT', 47123.456789);
const amount = exchange.amountToPrecision('BTC/USDT', 1.23456789);
```

### 时间戳转换

```javascript
// 当前时间戳
const now = exchange.milliseconds();

// 时间戳转日期字符串
const dateString = exchange.iso8601(now);

// 日期字符串转时间戳
const timestamp = exchange.parse8601('2022-01-01T00:00:00Z');
```

### 费用计算

```javascript
// 获取交易费用
const tradingFees = exchange.fees.trading;
console.log('Maker费率:', tradingFees.maker);
console.log('Taker费率:', tradingFees.taker);

// 计算订单费用
const cost = 1000;  // USDT
const fee = cost * tradingFees.taker;
console.log('预估手续费:', fee, 'USDT');
```

## 实战示例

### 简单的交易机器人

```python
import ccxt
import time

class SimpleBot:
    def __init__(self, exchange_id, api_key, secret):
        exchange_class = getattr(ccxt, exchange_id)
        self.exchange = exchange_class({
            'apiKey': api_key,
            'secret': secret,
            'sandbox': True,  # 测试环境
        })

    def get_price(self, symbol):
        """获取当前价格"""
        ticker = self.exchange.fetch_ticker(symbol)
        return ticker['last']

    def place_buy_order(self, symbol, amount, price):
        """下买单"""
        try:
            order = self.exchange.create_limit_buy_order(symbol, amount, price)
            print(f"买单已下达: {order['id']}")
            return order
        except Exception as e:
            print(f"下单失败: {e}")
            return None

    def place_sell_order(self, symbol, amount, price):
        """下卖单"""
        try:
            order = self.exchange.create_limit_sell_order(symbol, amount, price)
            print(f"卖单已下达: {order['id']}")
            return order
        except Exception as e:
            print(f"下单失败: {e}")
            return None

    def simple_strategy(self, symbol, buy_threshold, sell_threshold):
        """简单的买卖策略"""
        current_price = self.get_price(symbol)
        print(f"当前价格: {current_price}")

        # 这里可以实现你的交易策略
        # 例如：价格低于买入阈值时买入，高于卖出阈值时卖出

        if current_price < buy_threshold:
            self.place_buy_order(symbol, 0.001, current_price * 0.99)
        elif current_price > sell_threshold:
            self.place_sell_order(symbol, 0.001, current_price * 1.01)

# 使用示例
bot = SimpleBot('binance', 'your_api_key', 'your_secret')
bot.simple_strategy('BTC/USDT', 45000, 50000)
```

### 套利机器人示例

```python
import ccxt
import asyncio

class ArbitrageBot:
    def __init__(self):
        self.exchanges = {
            'binance': ccxt.binance(),
            'okx': ccxt.okx(),
            'bybit': ccxt.bybit()
        }

    async def get_prices(self, symbol):
        """获取所有交易所的价格"""
        prices = {}
        for name, exchange in self.exchanges.items():
            try:
                ticker = await exchange.fetch_ticker(symbol)
                prices[name] = {
                    'bid': ticker['bid'],
                    'ask': ticker['ask']
                }
            except Exception as e:
                print(f"获取{name}价格失败: {e}")
        return prices

    def find_arbitrage_opportunity(self, prices):
        """寻找套利机会"""
        opportunities = []

        for buy_exchange, buy_data in prices.items():
            for sell_exchange, sell_data in prices.items():
                if buy_exchange != sell_exchange:
                    profit_rate = (sell_data['bid'] - buy_data['ask']) / buy_data['ask']
                    if profit_rate > 0.005:  # 0.5%以上的套利机会
                        opportunities.append({
                            'buy_exchange': buy_exchange,
                            'sell_exchange': sell_exchange,
                            'buy_price': buy_data['ask'],
                            'sell_price': sell_data['bid'],
                            'profit_rate': profit_rate
                        })

        return opportunities

    async def run(self, symbol):
        """运行套利检测"""
        prices = await self.get_prices(symbol)
        opportunities = self.find_arbitrage_opportunity(prices)

        for opp in opportunities:
            print(f"套利机会: 在{opp['buy_exchange']}买入{opp['buy_price']}, "
                  f"在{opp['sell_exchange']}卖出{opp['sell_price']}, "
                  f"利润率: {opp['profit_rate']:.2%}")

# 使用示例
# bot = ArbitrageBot()
# asyncio.run(bot.run('BTC/USDT'))
```

### 网格交易策略

```python
import ccxt
import numpy as np

class GridTradingBot:
    def __init__(self, exchange, symbol, grid_size=10, grid_spacing=0.01):
        self.exchange = exchange
        self.symbol = symbol
        self.grid_size = grid_size
        self.grid_spacing = grid_spacing
        self.orders = {}

    def create_grid(self, center_price):
        """创建网格订单"""
        buy_orders = []
        sell_orders = []

        for i in range(1, self.grid_size + 1):
            # 买单价格
            buy_price = center_price * (1 - self.grid_spacing * i)
            # 卖单价格
            sell_price = center_price * (1 + self.grid_spacing * i)

            buy_orders.append(buy_price)
            sell_orders.append(sell_price)

        return buy_orders, sell_orders

    def place_grid_orders(self, buy_prices, sell_prices, amount):
        """下网格订单"""
        for price in buy_prices:
            try:
                order = self.exchange.create_limit_buy_order(
                    self.symbol, amount, price
                )
                self.orders[order['id']] = order
                print(f"买单已下达: {price}")
            except Exception as e:
                print(f"买单下达失败: {e}")

        for price in sell_prices:
            try:
                order = self.exchange.create_limit_sell_order(
                    self.symbol, amount, price
                )
                self.orders[order['id']] = order
                print(f"卖单已下达: {price}")
            except Exception as e:
                print(f"卖单下达失败: {e}")

    def check_filled_orders(self):
        """检查已成交订单"""
        for order_id in list(self.orders.keys()):
            try:
                order = self.exchange.fetch_order(order_id, self.symbol)
                if order['status'] == 'closed':
                    print(f"订单{order_id}已成交")
                    del self.orders[order_id]
                    # 在这里可以添加重新下单的逻辑
            except Exception as e:
                print(f"检查订单状态失败: {e}")

# 使用示例
# exchange = ccxt.binance({'apiKey': 'key', 'secret': 'secret'})
# bot = GridTradingBot(exchange, 'BTC/USDT')
# current_price = exchange.fetch_ticker('BTC/USDT')['last']
# buy_prices, sell_prices = bot.create_grid(current_price)
# bot.place_grid_orders(buy_prices, sell_prices, 0.001)
```

## 风险管理

### 止损止盈

```python
class RiskManager:
    def __init__(self, exchange):
        self.exchange = exchange

    def set_stop_loss(self, symbol, position_size, entry_price, stop_loss_pct=0.05):
        """设置止损"""
        stop_price = entry_price * (1 - stop_loss_pct)

        try:
            order = self.exchange.create_stop_market_order(
                symbol, 'sell', position_size, stop_price
            )
            print(f"止损单已设置: {stop_price}")
            return order
        except Exception as e:
            print(f"设置止损失败: {e}")
            return None

    def set_take_profit(self, symbol, position_size, entry_price, take_profit_pct=0.10):
        """设置止盈"""
        take_profit_price = entry_price * (1 + take_profit_pct)

        try:
            order = self.exchange.create_limit_sell_order(
                symbol, position_size, take_profit_price
            )
            print(f"止盈单已设置: {take_profit_price}")
            return order
        except Exception as e:
            print(f"设置止盈失败: {e}")
            return None

    def calculate_position_size(self, account_balance, risk_pct=0.02, entry_price=None, stop_price=None):
        """计算仓位大小"""
        if not entry_price or not stop_price:
            return None

        risk_amount = account_balance * risk_pct
        price_diff = abs(entry_price - stop_price)
        position_size = risk_amount / price_diff

        return position_size
```

### 资金管理

```python
class MoneyManager:
    def __init__(self, exchange):
        self.exchange = exchange

    def get_account_value(self):
        """获取账户总价值（以USDT计算）"""
        balance = self.exchange.fetch_balance()
        total_value = 0

        for currency, amount in balance['total'].items():
            if currency == 'USDT':
                total_value += amount
            elif amount > 0:
                try:
                    ticker = self.exchange.fetch_ticker(f'{currency}/USDT')
                    total_value += amount * ticker['last']
                except:
                    pass  # 忽略无法获取价格的币种

        return total_value

    def calculate_max_position_size(self, symbol, max_risk_pct=0.1):
        """计算最大仓位大小"""
        account_value = self.get_account_value()
        max_position_value = account_value * max_risk_pct

        ticker = self.exchange.fetch_ticker(symbol)
        current_price = ticker['last']

        max_position_size = max_position_value / current_price
        return max_position_size

    def diversify_portfolio(self, symbols, total_amount):
        """分散投资组合"""
        amount_per_symbol = total_amount / len(symbols)
        allocations = {}

        for symbol in symbols:
            ticker = self.exchange.fetch_ticker(symbol)
            price = ticker['last']
            size = amount_per_symbol / price
            allocations[symbol] = size

        return allocations
```

## 数据分析工具

### 技术指标计算

```python
import pandas as pd
import numpy as np

class TechnicalAnalysis:
    @staticmethod
    def sma(data, period):
        """简单移动平均线"""
        return data.rolling(window=period).mean()

    @staticmethod
    def ema(data, period):
        """指数移动平均线"""
        return data.ewm(span=period).mean()

    @staticmethod
    def rsi(data, period=14):
        """相对强弱指数"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    @staticmethod
    def bollinger_bands(data, period=20, std_dev=2):
        """布林带"""
        sma = data.rolling(window=period).mean()
        std = data.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        return upper_band, sma, lower_band

    @staticmethod
    def macd(data, fast=12, slow=26, signal=9):
        """MACD指标"""
        ema_fast = data.ewm(span=fast).mean()
        ema_slow = data.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram

# 使用示例
def analyze_symbol(exchange, symbol, timeframe='1h', limit=100):
    """分析交易对"""
    # 获取K线数据
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

    # 计算技术指标
    df['sma_20'] = TechnicalAnalysis.sma(df['close'], 20)
    df['ema_12'] = TechnicalAnalysis.ema(df['close'], 12)
    df['rsi'] = TechnicalAnalysis.rsi(df['close'])

    upper, middle, lower = TechnicalAnalysis.bollinger_bands(df['close'])
    df['bb_upper'] = upper
    df['bb_middle'] = middle
    df['bb_lower'] = lower

    macd, signal, histogram = TechnicalAnalysis.macd(df['close'])
    df['macd'] = macd
    df['macd_signal'] = signal
    df['macd_histogram'] = histogram

    return df

# exchange = ccxt.binance()
# analysis = analyze_symbol(exchange, 'BTC/USDT')
# print(analysis.tail())
```

## 监控和日志

### 交易日志记录

```python
import logging
from datetime import datetime
import json

class TradingLogger:
    def __init__(self, log_file='trading.log'):
        self.logger = logging.getLogger('TradingBot')
        self.logger.setLevel(logging.INFO)

        # 文件处理器
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    def log_order(self, action, order):
        """记录订单信息"""
        log_data = {
            'action': action,
            'order_id': order.get('id'),
            'symbol': order.get('symbol'),
            'type': order.get('type'),
            'side': order.get('side'),
            'amount': order.get('amount'),
            'price': order.get('price'),
            'status': order.get('status'),
            'timestamp': datetime.now().isoformat()
        }
        self.logger.info(f"ORDER_{action.upper()}: {json.dumps(log_data)}")

    def log_trade(self, trade):
        """记录交易信息"""
        log_data = {
            'trade_id': trade.get('id'),
            'symbol': trade.get('symbol'),
            'side': trade.get('side'),
            'amount': trade.get('amount'),
            'price': trade.get('price'),
            'cost': trade.get('cost'),
            'fee': trade.get('fee'),
            'timestamp': datetime.now().isoformat()
        }
        self.logger.info(f"TRADE: {json.dumps(log_data)}")

    def log_error(self, error, context=None):
        """记录错误信息"""
        error_data = {
            'error': str(error),
            'context': context,
            'timestamp': datetime.now().isoformat()
        }
        self.logger.error(f"ERROR: {json.dumps(error_data)}")

    def log_balance(self, balance):
        """记录余额信息"""
        self.logger.info(f"BALANCE: {json.dumps(balance['total'])}")

# 使用示例
logger = TradingLogger()

# 记录订单
# order = exchange.create_limit_buy_order('BTC/USDT', 0.001, 45000)
# logger.log_order('create', order)
```

### 性能监控

```python
import time
from functools import wraps

class PerformanceMonitor:
    def __init__(self):
        self.metrics = {}

    def time_function(self, func_name):
        """装饰器：测量函数执行时间"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()

                execution_time = end_time - start_time
                if func_name not in self.metrics:
                    self.metrics[func_name] = []
                self.metrics[func_name].append(execution_time)

                print(f"{func_name} 执行时间: {execution_time:.4f}秒")
                return result
            return wrapper
        return decorator

    def get_average_time(self, func_name):
        """获取函数平均执行时间"""
        if func_name in self.metrics:
            return sum(self.metrics[func_name]) / len(self.metrics[func_name])
        return 0

    def print_stats(self):
        """打印性能统计"""
        for func_name, times in self.metrics.items():
            avg_time = sum(times) / len(times)
            max_time = max(times)
            min_time = min(times)
            print(f"{func_name}:")
            print(f"  平均时间: {avg_time:.4f}秒")
            print(f"  最大时间: {max_time:.4f}秒")
            print(f"  最小时间: {min_time:.4f}秒")
            print(f"  调用次数: {len(times)}")

# 使用示例
monitor = PerformanceMonitor()

@monitor.time_function('fetch_ticker')
def fetch_ticker_with_monitoring(exchange, symbol):
    return exchange.fetch_ticker(symbol)

# exchange = ccxt.binance()
# ticker = fetch_ticker_with_monitoring(exchange, 'BTC/USDT')
# monitor.print_stats()
```

## 部署和生产环境

### Docker部署

创建Dockerfile：

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app

# 运行应用
CMD ["python", "main.py"]
```

requirements.txt：
```
ccxt>=4.4.0
pandas>=1.3.0
numpy>=1.21.0
python-dotenv>=0.19.0
```

docker-compose.yml：
```yaml
version: '3.8'
services:
  trading-bot:
    build: .
    environment:
      - API_KEY=${API_KEY}
      - API_SECRET=${API_SECRET}
      - ENVIRONMENT=production
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
```

### 环境变量管理

```python
import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # API配置
    API_KEY = os.getenv('API_KEY')
    API_SECRET = os.getenv('API_SECRET')
    API_PASSPHRASE = os.getenv('API_PASSPHRASE')  # OKX等需要

    # 交易所配置
    EXCHANGE = os.getenv('EXCHANGE', 'binance')
    SANDBOX = os.getenv('SANDBOX', 'false').lower() == 'true'

    # 交易配置
    SYMBOLS = os.getenv('SYMBOLS', 'BTC/USDT,ETH/USDT').split(',')
    MAX_POSITION_SIZE = float(os.getenv('MAX_POSITION_SIZE', '0.1'))
    RISK_PERCENTAGE = float(os.getenv('RISK_PERCENTAGE', '0.02'))

    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'trading.log')

    # 数据库配置（如果使用）
    DATABASE_URL = os.getenv('DATABASE_URL')

# 使用配置
config = Config()
exchange = getattr(ccxt, config.EXCHANGE)({
    'apiKey': config.API_KEY,
    'secret': config.API_SECRET,
    'sandbox': config.SANDBOX,
})
```

### 数据持久化

```python
import sqlite3
import json
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_path='trading.db'):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 订单表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id TEXT PRIMARY KEY,
                symbol TEXT,
                type TEXT,
                side TEXT,
                amount REAL,
                price REAL,
                status TEXT,
                timestamp INTEGER,
                raw_data TEXT
            )
        ''')

        # 交易表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id TEXT PRIMARY KEY,
                order_id TEXT,
                symbol TEXT,
                side TEXT,
                amount REAL,
                price REAL,
                cost REAL,
                fee_cost REAL,
                fee_currency TEXT,
                timestamp INTEGER,
                raw_data TEXT
            )
        ''')

        # 余额历史表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS balance_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp INTEGER,
                total_value REAL,
                balances TEXT
            )
        ''')

        conn.commit()
        conn.close()

    def save_order(self, order):
        """保存订单"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO orders
            (id, symbol, type, side, amount, price, status, timestamp, raw_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            order['id'],
            order['symbol'],
            order['type'],
            order['side'],
            order['amount'],
            order['price'],
            order['status'],
            order['timestamp'],
            json.dumps(order)
        ))

        conn.commit()
        conn.close()

    def save_trade(self, trade):
        """保存交易"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        fee = trade.get('fee', {})
        cursor.execute('''
            INSERT OR REPLACE INTO trades
            (id, order_id, symbol, side, amount, price, cost, fee_cost, fee_currency, timestamp, raw_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            trade['id'],
            trade.get('order'),
            trade['symbol'],
            trade['side'],
            trade['amount'],
            trade['price'],
            trade['cost'],
            fee.get('cost'),
            fee.get('currency'),
            trade['timestamp'],
            json.dumps(trade)
        ))

        conn.commit()
        conn.close()

    def save_balance_snapshot(self, balance, total_value):
        """保存余额快照"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO balance_history (timestamp, total_value, balances)
            VALUES (?, ?, ?)
        ''', (
            int(datetime.now().timestamp() * 1000),
            total_value,
            json.dumps(balance)
        ))

        conn.commit()
        conn.close()

    def get_trading_stats(self, days=30):
        """获取交易统计"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        since = int((datetime.now().timestamp() - days * 86400) * 1000)

        cursor.execute('''
            SELECT
                COUNT(*) as trade_count,
                SUM(cost) as total_volume,
                SUM(fee_cost) as total_fees,
                AVG(price) as avg_price
            FROM trades
            WHERE timestamp > ?
        ''', (since,))

        stats = cursor.fetchone()
        conn.close()

        return {
            'trade_count': stats[0],
            'total_volume': stats[1] or 0,
            'total_fees': stats[2] or 0,
            'avg_price': stats[3] or 0
        }
```

### 健康检查和监控

```python
import requests
import time
from threading import Thread

class HealthMonitor:
    def __init__(self, exchange, webhook_url=None):
        self.exchange = exchange
        self.webhook_url = webhook_url
        self.is_healthy = True
        self.last_check = time.time()

    def check_exchange_connection(self):
        """检查交易所连接"""
        try:
            self.exchange.fetch_ticker('BTC/USDT')
            return True
        except Exception as e:
            print(f"交易所连接检查失败: {e}")
            return False

    def check_api_limits(self):
        """检查API限制"""
        try:
            # 检查剩余请求次数
            response = self.exchange.fetch_ticker('BTC/USDT')
            # 某些交易所会在响应头中返回限制信息
            return True
        except Exception as e:
            print(f"API限制检查失败: {e}")
            return False

    def send_alert(self, message):
        """发送告警"""
        if self.webhook_url:
            try:
                payload = {
                    'text': f"🚨 交易机器人告警: {message}",
                    'timestamp': datetime.now().isoformat()
                }
                requests.post(self.webhook_url, json=payload)
            except Exception as e:
                print(f"发送告警失败: {e}")

        print(f"ALERT: {message}")

    def health_check(self):
        """执行健康检查"""
        current_time = time.time()

        # 检查交易所连接
        if not self.check_exchange_connection():
            if self.is_healthy:
                self.send_alert("交易所连接失败")
                self.is_healthy = False
        else:
            if not self.is_healthy:
                self.send_alert("交易所连接已恢复")
                self.is_healthy = True

        self.last_check = current_time

    def start_monitoring(self, interval=60):
        """启动监控线程"""
        def monitor():
            while True:
                self.health_check()
                time.sleep(interval)

        monitor_thread = Thread(target=monitor, daemon=True)
        monitor_thread.start()
        print("健康监控已启动")
```

### 完整的生产级机器人框架

```python
import ccxt
import time
import signal
import sys
from threading import Thread, Event
from datetime import datetime

class ProductionTradingBot:
    def __init__(self, config):
        self.config = config
        self.exchange = self.init_exchange()
        self.db = DatabaseManager()
        self.logger = TradingLogger()
        self.monitor = HealthMonitor(self.exchange)
        self.running = Event()
        self.running.set()

        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def init_exchange(self):
        """初始化交易所"""
        exchange_class = getattr(ccxt, self.config.EXCHANGE)
        return exchange_class({
            'apiKey': self.config.API_KEY,
            'secret': self.config.API_SECRET,
            'sandbox': self.config.SANDBOX,
            'enableRateLimit': True,
        })

    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"收到信号 {signum}，正在优雅关闭...")
        self.running.clear()

    def execute_strategy(self):
        """执行交易策略"""
        try:
            # 这里实现你的交易策略
            for symbol in self.config.SYMBOLS:
                ticker = self.exchange.fetch_ticker(symbol)
                self.logger.logger.info(f"{symbol} 当前价格: {ticker['last']}")

                # 示例策略逻辑
                # if self.should_buy(symbol, ticker):
                #     self.place_buy_order(symbol)
                # elif self.should_sell(symbol, ticker):
                #     self.place_sell_order(symbol)

        except Exception as e:
            self.logger.log_error(e, "执行策略时出错")

    def save_balance_snapshot(self):
        """保存余额快照"""
        try:
            balance = self.exchange.fetch_balance()
            # 计算总价值（简化版本）
            total_value = balance['USDT']['total'] if 'USDT' in balance else 0

            self.db.save_balance_snapshot(balance, total_value)
            self.logger.log_balance(balance)
        except Exception as e:
            self.logger.log_error(e, "保存余额快照时出错")

    def run(self):
        """主运行循环"""
        print("交易机器人启动中...")

        # 启动健康监控
        self.monitor.start_monitoring()

        # 启动余额快照线程
        def balance_snapshot_worker():
            while self.running.is_set():
                self.save_balance_snapshot()
                time.sleep(3600)  # 每小时保存一次

        balance_thread = Thread(target=balance_snapshot_worker, daemon=True)
        balance_thread.start()

        print("交易机器人已启动")

        # 主循环
        while self.running.is_set():
            try:
                self.execute_strategy()
                time.sleep(30)  # 30秒执行一次
            except KeyboardInterrupt:
                break
            except Exception as e:
                self.logger.log_error(e, "主循环出错")
                time.sleep(60)  # 出错后等待1分钟

        print("交易机器人已停止")

# 使用示例
if __name__ == "__main__":
    config = Config()
    bot = ProductionTradingBot(config)
    bot.run()
```

### 部署脚本

deploy.sh：
```bash
#!/bin/bash

# 构建Docker镜像
docker build -t trading-bot:latest .

# 停止旧容器
docker-compose down

# 启动新容器
docker-compose up -d

# 检查容器状态
docker-compose ps

# 查看日志
docker-compose logs -f trading-bot
```

### 监控脚本

monitor.py：
```python
#!/usr/bin/env python3
import subprocess
import time
import requests

def check_container_health():
    """检查容器健康状态"""
    try:
        result = subprocess.run(
            ['docker-compose', 'ps', '-q', 'trading-bot'],
            capture_output=True, text=True
        )
        return bool(result.stdout.strip())
    except:
        return False

def restart_container():
    """重启容器"""
    try:
        subprocess.run(['docker-compose', 'restart', 'trading-bot'])
        return True
    except:
        return False

def send_notification(message):
    """发送通知"""
    webhook_url = "YOUR_WEBHOOK_URL"
    if webhook_url:
        try:
            requests.post(webhook_url, json={'text': message})
        except:
            pass
    print(message)

def main():
    while True:
        if not check_container_health():
            send_notification("🚨 交易机器人容器已停止，正在重启...")
            if restart_container():
                send_notification("✅ 交易机器人容器已重启")
            else:
                send_notification("❌ 交易机器人容器重启失败")

        time.sleep(60)  # 每分钟检查一次

if __name__ == "__main__":
    main()
```

## 总结

CCXT是一个功能强大的加密货币交易库，支持多种编程语言和众多交易所。本文档涵盖了从基础使用到高级功能的各个方面：

### 主要特性
- **多语言支持**：JavaScript、Python、PHP、C#、Go
- **统一API**：标准化的接口，便于跨交易所开发
- **丰富功能**：支持现货、期货、期权等多种交易类型
- **实时数据**：通过CCXT Pro支持WebSocket数据流
- **生产就绪**：完善的错误处理和频率限制机制

### 适用场景
- 算法交易和量化策略
- 套利机器人开发
- 市场数据分析
- 投资组合管理
- 交易所API集成

### 最佳实践
1. **安全第一**：妥善保管API密钥，使用环境变量
2. **风险控制**：实施止损止盈和仓位管理
3. **错误处理**：完善的异常处理和重试机制
4. **监控日志**：详细的日志记录和性能监控
5. **测试验证**：先在测试环境验证策略

### 注意事项
- 交易有风险，投资需谨慎
- 充分测试后再投入实盘
- 关注交易所的API变更和维护公告
- 遵守各交易所的使用条款和频率限制

希望这份中文文档能帮助您更好地使用CCXT库进行加密货币交易开发！

---

*本文档基于CCXT v4.4.98版本编写，最后更新时间：2025年8月*
