<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>CCXT &ndash; &#x52a0;&#x5bc6;&#x8d27;&#x5e01;&#x4ea4;&#x6613;&#x6240;&#x4ea4;&#x6613;&#x5e93;</title>
            <style>
/* From extension vscode.github */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.vscode-dark img[src$=\#gh-light-mode-only],
.vscode-light img[src$=\#gh-dark-mode-only],
.vscode-high-contrast:not(.vscode-high-contrast-light) img[src$=\#gh-light-mode-only],
.vscode-high-contrast-light img[src$=\#gh-dark-mode-only] {
	display: none;
}

</style>
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex/dist/katex.min.css">
<link href="https://cdn.jsdelivr.net/npm/katex-copytex@latest/dist/katex-copytex.min.css" rel="stylesheet" type="text/css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/highlight.css">
<style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        </style>
        <style>
.task-list-item {
    list-style-type: none;
}

.task-list-item-checkbox {
    margin-left: -20px;
    vertical-align: middle;
    pointer-events: none;
}
</style>
<style>
:root {
  --color-note: #0969da;
  --color-tip: #1a7f37;
  --color-warning: #9a6700;
  --color-severe: #bc4c00;
  --color-caution: #d1242f;
  --color-important: #8250df;
}

</style>
<style>
@media (prefers-color-scheme: dark) {
  :root {
    --color-note: #2f81f7;
    --color-tip: #3fb950;
    --color-warning: #d29922;
    --color-severe: #db6d28;
    --color-caution: #f85149;
    --color-important: #a371f7;
  }
}

</style>
<style>
.markdown-alert {
  padding: 0.5rem 1rem;
  margin-bottom: 16px;
  color: inherit;
  border-left: .25em solid #888;
}

.markdown-alert>:first-child {
  margin-top: 0
}

.markdown-alert>:last-child {
  margin-bottom: 0
}

.markdown-alert .markdown-alert-title {
  display: flex;
  font-weight: 500;
  align-items: center;
  line-height: 1
}

.markdown-alert .markdown-alert-title .octicon {
  margin-right: 0.5rem;
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.markdown-alert.markdown-alert-note {
  border-left-color: var(--color-note);
}

.markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--color-note);
}

.markdown-alert.markdown-alert-important {
  border-left-color: var(--color-important);
}

.markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--color-important);
}

.markdown-alert.markdown-alert-warning {
  border-left-color: var(--color-warning);
}

.markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--color-warning);
}

.markdown-alert.markdown-alert-tip {
  border-left-color: var(--color-tip);
}

.markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--color-tip);
}

.markdown-alert.markdown-alert-caution {
  border-left-color: var(--color-caution);
}

.markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--color-caution);
}

</style>
        
        </head>
        <body class="vscode-body vscode-light">
            <h1 id="ccxt--加密货币交易所交易库">CCXT – 加密货币交易所交易库</h1>
<p><a href="https://www.npmjs.com/package/ccxt"><img src="https://img.shields.io/npm/dy/ccxt.svg" alt="NPM Downloads"></a> <a href="https://npmjs.com/package/ccxt"><img src="https://img.shields.io/npm/v/ccxt.svg" alt="npm"></a> <a href="https://pypi.python.org/pypi/ccxt"><img src="https://img.shields.io/pypi/v/ccxt.svg" alt="PyPI"></a> <a href="https://www.nuget.org/packages/ccxt"><img src="https://img.shields.io/nuget/v/ccxt" alt="NuGet version"></a> <a href="https://godoc.org/github.com/ccxt/ccxt/go/v4"><img src="https://pkg.go.dev/badge/github.com/ccxt/ccxt/go/v4?utm_source=godoc" alt="GoDoc"></a> <a href="https://discord.gg/ccxt"><img src="https://img.shields.io/discord/690203284119617602?logo=discord&amp;logoColor=white" alt="Discord"></a> <a href="https://github.com/ccxt/ccxt/wiki/Exchange-Markets"><img src="https://img.shields.io/badge/exchanges-106-blue.svg" alt="支持的交易所"></a></p>
<p>一个支持多种编程语言的加密货币交易和电子商务库，包括 <code>JavaScript</code>、<code>Python</code>、<code>PHP</code>、<code>C#</code>、<code>Go</code>，支持众多比特币/以太坊/山寨币交易所市场和商户API。</p>
<h2 id="目录">目录</h2>
<ul>
<li><a href="#%E5%AE%89%E8%A3%85">安装</a></li>
<li><a href="#%E4%BD%BF%E7%94%A8%E6%96%B9%E6%B3%95">使用方法</a></li>
<li><a href="#%E6%94%AF%E6%8C%81%E7%9A%84%E4%BA%A4%E6%98%93%E6%89%80">支持的交易所</a></li>
<li><a href="#%E5%8A%9F%E8%83%BD%E7%89%B9%E6%80%A7">功能特性</a></li>
<li><a href="#%E5%BF%AB%E9%80%9F%E5%BC%80%E5%A7%8B">快速开始</a></li>
<li><a href="#api%E6%96%87%E6%A1%A3">API文档</a></li>
<li><a href="#%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98">常见问题</a></li>
</ul>
<h2 id="概述">概述</h2>
<p><strong>CCXT</strong> 库用于连接和交易全球的加密货币交易所和支付处理服务。它提供对市场数据的快速访问，用于存储、分析、可视化、指标开发、算法交易、策略回测、机器人编程和相关软件工程。</p>
<p>它专为<strong>程序员、开发者、技术熟练的交易者、数据科学家和金融分析师</strong>构建交易算法而设计。</p>
<h3 id="当前功能列表">当前功能列表</h3>
<ul>
<li>支持众多加密货币交易所 — 更多即将推出</li>
<li>完全实现的公共和私有API</li>
<li>可选的标准化数据，用于跨交易所分析和套利</li>
<li>开箱即用的统一API，极易集成</li>
<li>支持 Node 10.4+、Python 3、PHP 8.1+、netstandard2.0/2.1、Go 1.20+ 和网页浏览器</li>
</ul>
<h2 id="安装">安装</h2>
<p>安装CCXT库最简单的方法是使用包管理器：</p>
<h3 id="javascript-npm">JavaScript (NPM)</h3>
<pre><code class="language-bash">npm install ccxt
</code></pre>
<pre><code class="language-javascript"><span class="hljs-comment">// CommonJS</span>
<span class="hljs-keyword">var</span> ccxt = <span class="hljs-built_in">require</span>(<span class="hljs-string">&#x27;ccxt&#x27;</span>)
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(ccxt.<span class="hljs-property">exchanges</span>) <span class="hljs-comment">// 打印所有可用交易所</span>

<span class="hljs-comment">// ES模块</span>
<span class="hljs-keyword">import</span> {version, exchanges} <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;ccxt&#x27;</span>;
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(version, <span class="hljs-title class_">Object</span>.<span class="hljs-title function_">keys</span>(exchanges));
</code></pre>
<h3 id="python">Python</h3>
<pre><code class="language-bash">pip install ccxt
</code></pre>
<pre><code class="language-python"><span class="hljs-keyword">import</span> ccxt
<span class="hljs-built_in">print</span>(ccxt.exchanges)  <span class="hljs-comment"># 打印所有可用交易所类的列表</span>
</code></pre>
<p>异步支持（Python 3.7.0+）：</p>
<pre><code class="language-python"><span class="hljs-keyword">import</span> ccxt.async_support <span class="hljs-keyword">as</span> ccxt  <span class="hljs-comment"># 链接到ccxt的异步版本</span>
</code></pre>
<h3 id="php">PHP</h3>
<pre><code class="language-bash">composer require ccxt/ccxt
</code></pre>
<pre><code class="language-php"><span class="hljs-keyword">include</span> <span class="hljs-string">&quot;ccxt.php&quot;</span>;
<span class="hljs-title function_ invoke__">var_dump</span>(\ccxt<span class="hljs-title class_">\Exchange</span>::<span class="hljs-variable">$exchanges</span>); <span class="hljs-comment">// 打印所有可用交易所类的列表</span>
</code></pre>
<h3 id="c">C#</h3>
<pre><code class="language-bash">dotnet add package ccxt
</code></pre>
<pre><code class="language-csharp"><span class="hljs-keyword">using</span> ccxt;
Console.WriteLine(ccxt.Exchanges) <span class="hljs-comment">// 稍后检查</span>
</code></pre>
<h3 id="go">Go</h3>
<pre><code class="language-bash">go install github.com/ccxt/ccxt/go/v4@latest
</code></pre>
<pre><code class="language-go"><span class="hljs-keyword">import</span> <span class="hljs-string">&quot;ccxt&quot;</span>
fmt.Println(ccxt.Exchanges)
</code></pre>
<h2 id="支持的交易所">支持的交易所</h2>
<p>CCXT库目前支持以下106个加密货币交易所市场和交易API：</p>
<h3 id="认证交易所">认证交易所</h3>
<table>
<thead>
<tr>
<th>交易所</th>
<th>ID</th>
<th>名称</th>
<th>版本</th>
<th>类型</th>
<th>认证</th>
<th>Pro版本</th>
<th>折扣</th>
</tr>
</thead>
<tbody>
<tr>
<td><img src="https://github.com/user-attachments/assets/e9419b93-ccb0-46aa-9bff-c883f096274b" alt="binance"></td>
<td>binance</td>
<td><a href="https://accounts.binance.com/en/register?ref=D7YA7CLY">Binance</a></td>
<td>*</td>
<td>CEX</td>
<td>✅</td>
<td>✅</td>
<td>10%</td>
</tr>
<tr>
<td><img src="https://github.com/user-attachments/assets/97a5d0b3-de10-423d-90e1-6620960025ed" alt="bybit"></td>
<td>bybit</td>
<td><a href="https://www.bybit.com/register?affiliate_id=35953">Bybit</a></td>
<td>5</td>
<td>CEX</td>
<td>✅</td>
<td>✅</td>
<td>-</td>
</tr>
<tr>
<td><img src="https://user-images.githubusercontent.com/1294454/*********-38b19e4a-bece-4dec-979a-5982859ffc04.jpg" alt="okx"></td>
<td>okx</td>
<td><a href="https://www.okx.com/join/CCXT2023">OKX</a></td>
<td>5</td>
<td>CEX</td>
<td>✅</td>
<td>✅</td>
<td>20%</td>
</tr>
</tbody>
</table>
<p><em>更多交易所请参考完整列表...</em></p>
<h2 id="功能特性">功能特性</h2>
<h3 id="公共api无需api密钥">公共API（无需API密钥）</h3>
<ul>
<li>市场数据</li>
<li>交易对/工具</li>
<li>价格信息（汇率）</li>
<li>订单簿</li>
<li>交易历史</li>
<li>行情数据</li>
<li>OHLC(V) K线图数据</li>
<li>其他公共端点</li>
</ul>
<h3 id="私有api需要api密钥">私有API（需要API密钥）</h3>
<ul>
<li>管理个人账户信息</li>
<li>查询账户余额</li>
<li>通过市价单和限价单进行交易</li>
<li>存取法币和加密货币资金</li>
<li>查询个人订单</li>
<li>获取账本历史</li>
<li>在账户间转移资金</li>
<li>使用商户服务</li>
</ul>
<h2 id="快速开始">快速开始</h2>
<h3 id="javascript-示例">JavaScript 示例</h3>
<pre><code class="language-javascript"><span class="hljs-meta">&#x27;use strict&#x27;</span>;
<span class="hljs-keyword">const</span> ccxt = <span class="hljs-built_in">require</span>(<span class="hljs-string">&#x27;ccxt&#x27;</span>);

(<span class="hljs-keyword">async</span> <span class="hljs-keyword">function</span> (<span class="hljs-params"></span>) {
    <span class="hljs-keyword">let</span> exchange = <span class="hljs-keyword">new</span> ccxt.<span class="hljs-title function_">binance</span>({
        <span class="hljs-string">&#x27;apiKey&#x27;</span>: <span class="hljs-string">&#x27;YOUR_API_KEY&#x27;</span>,
        <span class="hljs-string">&#x27;secret&#x27;</span>: <span class="hljs-string">&#x27;YOUR_SECRET&#x27;</span>,
    });

    <span class="hljs-comment">// 获取市场数据</span>
    <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">loadMarkets</span>());
    
    <span class="hljs-comment">// 获取订单簿</span>
    <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">fetchOrderBook</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>));
    
    <span class="hljs-comment">// 获取行情</span>
    <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">fetchTicker</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>));
    
    <span class="hljs-comment">// 获取账户余额</span>
    <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">fetchBalance</span>());
    
    <span class="hljs-comment">// 下单（市价卖单）</span>
    <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">createMarketSellOrder</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, <span class="hljs-number">0.001</span>));
    
    <span class="hljs-comment">// 下单（限价买单）</span>
    <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">createLimitBuyOrder</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, <span class="hljs-number">0.001</span>, <span class="hljs-number">50000</span>));
})();
</code></pre>
<h3 id="python-示例">Python 示例</h3>
<pre><code class="language-python"><span class="hljs-keyword">import</span> ccxt

<span class="hljs-comment"># 初始化交易所</span>
exchange = ccxt.binance({
    <span class="hljs-string">&#x27;apiKey&#x27;</span>: <span class="hljs-string">&#x27;YOUR_API_KEY&#x27;</span>,
    <span class="hljs-string">&#x27;secret&#x27;</span>: <span class="hljs-string">&#x27;YOUR_SECRET&#x27;</span>,
})

<span class="hljs-comment"># 加载市场</span>
markets = exchange.load_markets()
<span class="hljs-built_in">print</span>(exchange.<span class="hljs-built_in">id</span>, markets)

<span class="hljs-comment"># 获取订单簿</span>
orderbook = exchange.fetch_order_book(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>)
<span class="hljs-built_in">print</span>(orderbook)

<span class="hljs-comment"># 获取行情</span>
ticker = exchange.fetch_ticker(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>)
<span class="hljs-built_in">print</span>(ticker)

<span class="hljs-comment"># 获取余额</span>
balance = exchange.fetch_balance()
<span class="hljs-built_in">print</span>(balance)

<span class="hljs-comment"># 市价卖单</span>
order = exchange.create_market_sell_order(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, <span class="hljs-number">0.001</span>)
<span class="hljs-built_in">print</span>(order)

<span class="hljs-comment"># 限价买单</span>
order = exchange.create_limit_buy_order(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, <span class="hljs-number">0.001</span>, <span class="hljs-number">50000</span>)
<span class="hljs-built_in">print</span>(order)
</code></pre>
<h2 id="api方法">API方法</h2>
<h3 id="市场数据方法">市场数据方法</h3>
<ul>
<li><code>loadMarkets()</code> - 加载所有可用市场</li>
<li><code>fetchTicker(symbol)</code> - 获取单个交易对的行情</li>
<li><code>fetchTickers([symbols])</code> - 获取多个交易对的行情</li>
<li><code>fetchOrderBook(symbol)</code> - 获取订单簿</li>
<li><code>fetchTrades(symbol)</code> - 获取最近交易</li>
<li><code>fetchOHLCV(symbol, timeframe)</code> - 获取K线数据</li>
</ul>
<h3 id="交易方法">交易方法</h3>
<ul>
<li><code>fetchBalance()</code> - 获取账户余额</li>
<li><code>createOrder(symbol, type, side, amount, price, params)</code> - 创建订单</li>
<li><code>createMarketBuyOrder(symbol, amount)</code> - 创建市价买单</li>
<li><code>createMarketSellOrder(symbol, amount)</code> - 创建市价卖单</li>
<li><code>createLimitBuyOrder(symbol, amount, price)</code> - 创建限价买单</li>
<li><code>createLimitSellOrder(symbol, amount, price)</code> - 创建限价卖单</li>
<li><code>cancelOrder(id, symbol)</code> - 取消订单</li>
<li><code>fetchOrder(id, symbol)</code> - 获取订单信息</li>
<li><code>fetchOrders(symbol)</code> - 获取所有订单</li>
<li><code>fetchOpenOrders(symbol)</code> - 获取未完成订单</li>
<li><code>fetchClosedOrders(symbol)</code> - 获取已完成订单</li>
</ul>
<h3 id="账户方法">账户方法</h3>
<ul>
<li><code>fetchMyTrades(symbol)</code> - 获取个人交易历史</li>
<li><code>fetchDeposits()</code> - 获取充值记录</li>
<li><code>fetchWithdrawals()</code> - 获取提现记录</li>
<li><code>withdraw(currency, amount, address, tag, params)</code> - 提现</li>
</ul>
<h2 id="错误处理">错误处理</h2>
<p>CCXT定义了一套标准的异常类型：</p>
<pre><code class="language-python"><span class="hljs-keyword">try</span>:
    ticker = exchange.fetch_ticker(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>)
<span class="hljs-keyword">except</span> ccxt.NetworkError <span class="hljs-keyword">as</span> e:
    <span class="hljs-built_in">print</span>(<span class="hljs-string">&#x27;网络错误:&#x27;</span>, e)
<span class="hljs-keyword">except</span> ccxt.ExchangeError <span class="hljs-keyword">as</span> e:
    <span class="hljs-built_in">print</span>(<span class="hljs-string">&#x27;交易所错误:&#x27;</span>, e)
<span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
    <span class="hljs-built_in">print</span>(<span class="hljs-string">&#x27;其他错误:&#x27;</span>, e)
</code></pre>
<h2 id="配置选项">配置选项</h2>
<pre><code class="language-javascript"><span class="hljs-keyword">let</span> exchange = <span class="hljs-keyword">new</span> ccxt.<span class="hljs-title function_">binance</span>({
    <span class="hljs-string">&#x27;apiKey&#x27;</span>: <span class="hljs-string">&#x27;your_api_key&#x27;</span>,
    <span class="hljs-string">&#x27;secret&#x27;</span>: <span class="hljs-string">&#x27;your_secret&#x27;</span>,
    <span class="hljs-string">&#x27;timeout&#x27;</span>: <span class="hljs-number">30000</span>,        <span class="hljs-comment">// 请求超时时间（毫秒）</span>
    <span class="hljs-string">&#x27;rateLimit&#x27;</span>: <span class="hljs-number">1200</span>,       <span class="hljs-comment">// 请求频率限制（毫秒）</span>
    <span class="hljs-string">&#x27;verbose&#x27;</span>: <span class="hljs-literal">true</span>,         <span class="hljs-comment">// 详细日志</span>
    <span class="hljs-string">&#x27;sandbox&#x27;</span>: <span class="hljs-literal">true</span>,         <span class="hljs-comment">// 使用测试环境</span>
})
</code></pre>
<h2 id="常见问题">常见问题</h2>
<h3 id="1-如何获取api密钥">1. 如何获取API密钥？</h3>
<p>访问相应交易所的官方网站，注册账户后在API管理页面创建API密钥。</p>
<h3 id="2-支持哪些订单类型">2. 支持哪些订单类型？</h3>
<ul>
<li><code>market</code> - 市价单</li>
<li><code>limit</code> - 限价单</li>
<li><code>stop</code> - 止损单（部分交易所支持）</li>
<li><code>stop_limit</code> - 止损限价单（部分交易所支持）</li>
</ul>
<h3 id="3-如何处理频率限制">3. 如何处理频率限制？</h3>
<p>CCXT内置了频率限制处理，会自动等待适当的时间间隔。你也可以通过<code>rateLimit</code>参数调整。</p>
<h3 id="4-支持websocket吗">4. 支持WebSocket吗？</h3>
<p>是的，CCXT Pro版本支持WebSocket实时数据流。</p>
<h2 id="许可证">许可证</h2>
<p>CCXT采用MIT许可证，这意味着任何开发者都可以免费用于商业和开源软件开发，但使用时需自担风险，不提供任何保证。</p>
<h2 id="联系我们">联系我们</h2>
<ul>
<li>商务咨询：<EMAIL></li>
<li>GitHub：<a href="https://github.com/ccxt/ccxt">https://github.com/ccxt/ccxt</a></li>
<li>Discord：<a href="https://discord.gg/ccxt">https://discord.gg/ccxt</a></li>
<li>Twitter：<a href="https://twitter.com/ccxt_official">https://twitter.com/ccxt_official</a></li>
</ul>
<h2 id="免责声明">免责声明</h2>
<p>CCXT不是服务或服务器，而是一个软件。CCXT是MIT许可证下的免费开源非托管API代理软件。</p>
<ul>
<li><strong>非托管</strong>意味着CCXT不是交易中介，在任何时候都不持有交易者的资金</li>
<li><strong>MIT许可证</strong>意味着CCXT可用于任何目的，但使用时需自担风险，不提供任何保证</li>
<li><strong>免费软件</strong>意味着CCXT免费使用，没有隐藏费用</li>
</ul>
<h2 id="高级用法">高级用法</h2>
<h3 id="统一api-vs-交易所特定api">统一API vs 交易所特定API</h3>
<p>CCXT提供两种API访问方式：</p>
<h4 id="统一api推荐">统一API（推荐）</h4>
<pre><code class="language-javascript"><span class="hljs-comment">// 统一的方法名，适用于所有交易所</span>
<span class="hljs-keyword">const</span> ticker = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">fetchTicker</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>);
<span class="hljs-keyword">const</span> balance = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">fetchBalance</span>();
</code></pre>
<h4 id="交易所特定api">交易所特定API</h4>
<pre><code class="language-javascript"><span class="hljs-comment">// 直接调用交易所原生API</span>
<span class="hljs-keyword">const</span> response = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">publicGetTicker24hr</span>({
    <span class="hljs-string">&#x27;symbol&#x27;</span>: <span class="hljs-string">&#x27;BTCUSDT&#x27;</span>
});
</code></pre>
<h3 id="分页和限制">分页和限制</h3>
<p>许多API方法支持分页参数：</p>
<pre><code class="language-python"><span class="hljs-comment"># 获取最近100笔交易</span>
trades = exchange.fetch_trades(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, limit=<span class="hljs-number">100</span>)

<span class="hljs-comment"># 获取指定时间后的交易</span>
since = exchange.milliseconds() - <span class="hljs-number">86400000</span>  <span class="hljs-comment"># 24小时前</span>
trades = exchange.fetch_trades(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, since=since)

<span class="hljs-comment"># 分页获取订单</span>
orders = exchange.fetch_orders(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, since=since, limit=<span class="hljs-number">50</span>)
</code></pre>
<h3 id="自定义参数">自定义参数</h3>
<p>可以向API方法传递交易所特定的参数：</p>
<pre><code class="language-javascript"><span class="hljs-comment">// Binance特定参数</span>
<span class="hljs-keyword">const</span> order = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">createOrder</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, <span class="hljs-string">&#x27;limit&#x27;</span>, <span class="hljs-string">&#x27;buy&#x27;</span>, <span class="hljs-number">1</span>, <span class="hljs-number">50000</span>, {
    <span class="hljs-string">&#x27;timeInForce&#x27;</span>: <span class="hljs-string">&#x27;GTC&#x27;</span>,  <span class="hljs-comment">// Good Till Cancelled</span>
    <span class="hljs-string">&#x27;recvWindow&#x27;</span>: <span class="hljs-number">5000</span>
});

<span class="hljs-comment">// OKX特定参数</span>
<span class="hljs-keyword">const</span> order = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">createOrder</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, <span class="hljs-string">&#x27;limit&#x27;</span>, <span class="hljs-string">&#x27;buy&#x27;</span>, <span class="hljs-number">1</span>, <span class="hljs-number">50000</span>, {
    <span class="hljs-string">&#x27;tdMode&#x27;</span>: <span class="hljs-string">&#x27;cash&#x27;</span>,      <span class="hljs-comment">// 交易模式</span>
    <span class="hljs-string">&#x27;ccy&#x27;</span>: <span class="hljs-string">&#x27;USDT&#x27;</span>         <span class="hljs-comment">// 保证金币种</span>
});
</code></pre>
<h2 id="数据结构">数据结构</h2>
<h3 id="行情数据结构">行情数据结构</h3>
<pre><code class="language-javascript">{
    <span class="hljs-string">&#x27;symbol&#x27;</span>: <span class="hljs-string">&#x27;BTC/USDT&#x27;</span>,
    <span class="hljs-string">&#x27;timestamp&#x27;</span>: <span class="hljs-number">1640995200000</span>,
    <span class="hljs-string">&#x27;datetime&#x27;</span>: <span class="hljs-string">&#x27;2022-01-01T00:00:00.000Z&#x27;</span>,
    <span class="hljs-string">&#x27;high&#x27;</span>: <span class="hljs-number">47800.0</span>,
    <span class="hljs-string">&#x27;low&#x27;</span>: <span class="hljs-number">46200.0</span>,
    <span class="hljs-string">&#x27;bid&#x27;</span>: <span class="hljs-number">47000.0</span>,
    <span class="hljs-string">&#x27;bidVolume&#x27;</span>: <span class="hljs-number">1.5</span>,
    <span class="hljs-string">&#x27;ask&#x27;</span>: <span class="hljs-number">47100.0</span>,
    <span class="hljs-string">&#x27;askVolume&#x27;</span>: <span class="hljs-number">2.0</span>,
    <span class="hljs-string">&#x27;vwap&#x27;</span>: <span class="hljs-number">47000.0</span>,
    <span class="hljs-string">&#x27;open&#x27;</span>: <span class="hljs-number">46500.0</span>,
    <span class="hljs-string">&#x27;close&#x27;</span>: <span class="hljs-number">47000.0</span>,
    <span class="hljs-string">&#x27;last&#x27;</span>: <span class="hljs-number">47000.0</span>,
    <span class="hljs-string">&#x27;previousClose&#x27;</span>: <span class="hljs-number">46500.0</span>,
    <span class="hljs-string">&#x27;change&#x27;</span>: <span class="hljs-number">500.0</span>,
    <span class="hljs-string">&#x27;percentage&#x27;</span>: <span class="hljs-number">1.075</span>,
    <span class="hljs-string">&#x27;average&#x27;</span>: <span class="hljs-number">46750.0</span>,
    <span class="hljs-string">&#x27;baseVolume&#x27;</span>: <span class="hljs-number">1000.0</span>,
    <span class="hljs-string">&#x27;quoteVolume&#x27;</span>: <span class="hljs-number">47000000.0</span>,
    <span class="hljs-string">&#x27;info&#x27;</span>: {...}  <span class="hljs-comment">// 原始响应数据</span>
}
</code></pre>
<h3 id="订单数据结构">订单数据结构</h3>
<pre><code class="language-javascript">{
    <span class="hljs-string">&#x27;id&#x27;</span>: <span class="hljs-string">&#x27;12345&#x27;</span>,
    <span class="hljs-string">&#x27;clientOrderId&#x27;</span>: <span class="hljs-string">&#x27;myOrder123&#x27;</span>,
    <span class="hljs-string">&#x27;timestamp&#x27;</span>: <span class="hljs-number">1640995200000</span>,
    <span class="hljs-string">&#x27;datetime&#x27;</span>: <span class="hljs-string">&#x27;2022-01-01T00:00:00.000Z&#x27;</span>,
    <span class="hljs-string">&#x27;lastTradeTimestamp&#x27;</span>: <span class="hljs-number">1640995300000</span>,
    <span class="hljs-string">&#x27;symbol&#x27;</span>: <span class="hljs-string">&#x27;BTC/USDT&#x27;</span>,
    <span class="hljs-string">&#x27;type&#x27;</span>: <span class="hljs-string">&#x27;limit&#x27;</span>,
    <span class="hljs-string">&#x27;side&#x27;</span>: <span class="hljs-string">&#x27;buy&#x27;</span>,
    <span class="hljs-string">&#x27;amount&#x27;</span>: <span class="hljs-number">1.0</span>,
    <span class="hljs-string">&#x27;price&#x27;</span>: <span class="hljs-number">47000.0</span>,
    <span class="hljs-string">&#x27;cost&#x27;</span>: <span class="hljs-number">47000.0</span>,
    <span class="hljs-string">&#x27;average&#x27;</span>: <span class="hljs-number">47000.0</span>,
    <span class="hljs-string">&#x27;filled&#x27;</span>: <span class="hljs-number">1.0</span>,
    <span class="hljs-string">&#x27;remaining&#x27;</span>: <span class="hljs-number">0.0</span>,
    <span class="hljs-string">&#x27;status&#x27;</span>: <span class="hljs-string">&#x27;closed&#x27;</span>,
    <span class="hljs-string">&#x27;fee&#x27;</span>: {
        <span class="hljs-string">&#x27;currency&#x27;</span>: <span class="hljs-string">&#x27;USDT&#x27;</span>,
        <span class="hljs-string">&#x27;cost&#x27;</span>: <span class="hljs-number">47.0</span>,
        <span class="hljs-string">&#x27;rate&#x27;</span>: <span class="hljs-number">0.001</span>
    },
    <span class="hljs-string">&#x27;trades&#x27;</span>: [...],
    <span class="hljs-string">&#x27;info&#x27;</span>: {...}
}
</code></pre>
<h3 id="余额数据结构">余额数据结构</h3>
<pre><code class="language-javascript">{
    <span class="hljs-string">&#x27;info&#x27;</span>: {...},  <span class="hljs-comment">// 原始响应</span>
    <span class="hljs-string">&#x27;BTC&#x27;</span>: {
        <span class="hljs-string">&#x27;free&#x27;</span>: <span class="hljs-number">1.0</span>,      <span class="hljs-comment">// 可用余额</span>
        <span class="hljs-string">&#x27;used&#x27;</span>: <span class="hljs-number">0.5</span>,      <span class="hljs-comment">// 冻结余额</span>
        <span class="hljs-string">&#x27;total&#x27;</span>: <span class="hljs-number">1.5</span>      <span class="hljs-comment">// 总余额</span>
    },
    <span class="hljs-string">&#x27;USDT&#x27;</span>: {
        <span class="hljs-string">&#x27;free&#x27;</span>: <span class="hljs-number">10000.0</span>,
        <span class="hljs-string">&#x27;used&#x27;</span>: <span class="hljs-number">5000.0</span>,
        <span class="hljs-string">&#x27;total&#x27;</span>: <span class="hljs-number">15000.0</span>
    },
    <span class="hljs-string">&#x27;free&#x27;</span>: {
        <span class="hljs-string">&#x27;BTC&#x27;</span>: <span class="hljs-number">1.0</span>,
        <span class="hljs-string">&#x27;USDT&#x27;</span>: <span class="hljs-number">10000.0</span>
    },
    <span class="hljs-string">&#x27;used&#x27;</span>: {
        <span class="hljs-string">&#x27;BTC&#x27;</span>: <span class="hljs-number">0.5</span>,
        <span class="hljs-string">&#x27;USDT&#x27;</span>: <span class="hljs-number">5000.0</span>
    },
    <span class="hljs-string">&#x27;total&#x27;</span>: {
        <span class="hljs-string">&#x27;BTC&#x27;</span>: <span class="hljs-number">1.5</span>,
        <span class="hljs-string">&#x27;USDT&#x27;</span>: <span class="hljs-number">15000.0</span>
    }
}
</code></pre>
<h2 id="websocket支持ccxt-pro">WebSocket支持（CCXT Pro）</h2>
<p>CCXT Pro提供WebSocket实时数据流支持：</p>
<h3 id="安装ccxt-pro">安装CCXT Pro</h3>
<pre><code class="language-bash">npm install ccxt.pro
</code></pre>
<h3 id="实时行情数据">实时行情数据</h3>
<pre><code class="language-javascript"><span class="hljs-keyword">const</span> ccxtpro = <span class="hljs-built_in">require</span>(<span class="hljs-string">&#x27;ccxt.pro&#x27;</span>);

<span class="hljs-keyword">const</span> exchange = <span class="hljs-keyword">new</span> ccxtpro.<span class="hljs-title function_">binance</span>();

<span class="hljs-comment">// 订阅实时行情</span>
<span class="hljs-keyword">const</span> ticker = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">watchTicker</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>);
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(ticker);

<span class="hljs-comment">// 订阅实时订单簿</span>
<span class="hljs-keyword">const</span> orderbook = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">watchOrderBook</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>);
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(orderbook);

<span class="hljs-comment">// 订阅实时交易</span>
<span class="hljs-keyword">const</span> trades = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">watchTrades</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>);
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(trades);
</code></pre>
<h3 id="实时账户数据">实时账户数据</h3>
<pre><code class="language-javascript"><span class="hljs-comment">// 订阅余额变化</span>
<span class="hljs-keyword">const</span> balance = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">watchBalance</span>();
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(balance);

<span class="hljs-comment">// 订阅订单状态变化</span>
<span class="hljs-keyword">const</span> orders = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">watchOrders</span>();
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(orders);

<span class="hljs-comment">// 订阅个人交易</span>
<span class="hljs-keyword">const</span> myTrades = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">watchMyTrades</span>();
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(myTrades);
</code></pre>
<h2 id="测试环境">测试环境</h2>
<p>大多数交易所都提供测试环境（沙盒）：</p>
<pre><code class="language-javascript"><span class="hljs-keyword">const</span> exchange = <span class="hljs-keyword">new</span> ccxt.<span class="hljs-title function_">binance</span>({
    <span class="hljs-string">&#x27;apiKey&#x27;</span>: <span class="hljs-string">&#x27;testnet_api_key&#x27;</span>,
    <span class="hljs-string">&#x27;secret&#x27;</span>: <span class="hljs-string">&#x27;testnet_secret&#x27;</span>,
    <span class="hljs-string">&#x27;sandbox&#x27;</span>: <span class="hljs-literal">true</span>,  <span class="hljs-comment">// 启用测试环境</span>
});
</code></pre>
<h2 id="代理设置">代理设置</h2>
<p>如果需要通过代理访问：</p>
<pre><code class="language-javascript"><span class="hljs-keyword">const</span> exchange = <span class="hljs-keyword">new</span> ccxt.<span class="hljs-title function_">binance</span>({
    <span class="hljs-string">&#x27;proxy&#x27;</span>: <span class="hljs-string">&#x27;http://proxy.example.com:8080&#x27;</span>,
    <span class="hljs-comment">// 或者使用SOCKS代理</span>
    <span class="hljs-string">&#x27;proxy&#x27;</span>: <span class="hljs-string">&#x27;socks://proxy.example.com:1080&#x27;</span>,
});
</code></pre>
<h2 id="多线程异步处理">多线程/异步处理</h2>
<h3 id="python异步示例">Python异步示例</h3>
<pre><code class="language-python"><span class="hljs-keyword">import</span> asyncio
<span class="hljs-keyword">import</span> ccxt.async_support <span class="hljs-keyword">as</span> ccxt

<span class="hljs-keyword">async</span> <span class="hljs-keyword">def</span> <span class="hljs-title function_">main</span>():
    exchange = ccxt.binance()

    <span class="hljs-comment"># 并发获取多个交易对的行情</span>
    symbols = [<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, <span class="hljs-string">&#x27;ETH/USDT&#x27;</span>, <span class="hljs-string">&#x27;BNB/USDT&#x27;</span>]
    tasks = [exchange.fetch_ticker(symbol) <span class="hljs-keyword">for</span> symbol <span class="hljs-keyword">in</span> symbols]
    tickers = <span class="hljs-keyword">await</span> asyncio.gather(*tasks)

    <span class="hljs-keyword">for</span> ticker <span class="hljs-keyword">in</span> tickers:
        <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;<span class="hljs-subst">{ticker[<span class="hljs-string">&#x27;symbol&#x27;</span>]}</span>: <span class="hljs-subst">{ticker[<span class="hljs-string">&#x27;last&#x27;</span>]}</span>&quot;</span>)

    <span class="hljs-keyword">await</span> exchange.close()

asyncio.run(main())
</code></pre>
<h3 id="javascript并发示例">JavaScript并发示例</h3>
<pre><code class="language-javascript"><span class="hljs-keyword">const</span> exchange = <span class="hljs-keyword">new</span> ccxt.<span class="hljs-title function_">binance</span>();

<span class="hljs-keyword">async</span> <span class="hljs-keyword">function</span> <span class="hljs-title function_">fetchMultipleTickers</span>(<span class="hljs-params"></span>) {
    <span class="hljs-keyword">const</span> symbols = [<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, <span class="hljs-string">&#x27;ETH/USDT&#x27;</span>, <span class="hljs-string">&#x27;BNB/USDT&#x27;</span>];

    <span class="hljs-keyword">const</span> promises = symbols.<span class="hljs-title function_">map</span>(<span class="hljs-function"><span class="hljs-params">symbol</span> =&gt;</span>
        exchange.<span class="hljs-title function_">fetchTicker</span>(symbol)
    );

    <span class="hljs-keyword">const</span> tickers = <span class="hljs-keyword">await</span> <span class="hljs-title class_">Promise</span>.<span class="hljs-title function_">all</span>(promises);

    tickers.<span class="hljs-title function_">forEach</span>(<span class="hljs-function"><span class="hljs-params">ticker</span> =&gt;</span> {
        <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">`<span class="hljs-subst">${ticker.symbol}</span>: <span class="hljs-subst">${ticker.last}</span>`</span>);
    });
}

<span class="hljs-title function_">fetchMultipleTickers</span>();
</code></pre>
<h2 id="错误处理最佳实践">错误处理最佳实践</h2>
<pre><code class="language-python"><span class="hljs-keyword">import</span> ccxt
<span class="hljs-keyword">import</span> time

<span class="hljs-keyword">def</span> <span class="hljs-title function_">safe_fetch_ticker</span>(<span class="hljs-params">exchange, symbol, max_retries=<span class="hljs-number">3</span></span>):
    <span class="hljs-keyword">for</span> attempt <span class="hljs-keyword">in</span> <span class="hljs-built_in">range</span>(max_retries):
        <span class="hljs-keyword">try</span>:
            <span class="hljs-keyword">return</span> exchange.fetch_ticker(symbol)
        <span class="hljs-keyword">except</span> ccxt.NetworkError <span class="hljs-keyword">as</span> e:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&#x27;网络错误 (尝试 <span class="hljs-subst">{attempt + <span class="hljs-number">1</span>}</span>/<span class="hljs-subst">{max_retries}</span>): <span class="hljs-subst">{e}</span>&#x27;</span>)
            <span class="hljs-keyword">if</span> attempt &lt; max_retries - <span class="hljs-number">1</span>:
                time.sleep(<span class="hljs-number">2</span> ** attempt)  <span class="hljs-comment"># 指数退避</span>
        <span class="hljs-keyword">except</span> ccxt.ExchangeError <span class="hljs-keyword">as</span> e:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&#x27;交易所错误: <span class="hljs-subst">{e}</span>&#x27;</span>)
            <span class="hljs-keyword">break</span>
        <span class="hljs-keyword">except</span> ccxt.RateLimitExceeded <span class="hljs-keyword">as</span> e:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&#x27;频率限制: <span class="hljs-subst">{e}</span>&#x27;</span>)
            time.sleep(exchange.rateLimit / <span class="hljs-number">1000</span>)

    <span class="hljs-keyword">return</span> <span class="hljs-literal">None</span>

<span class="hljs-comment"># 使用示例</span>
exchange = ccxt.binance()
ticker = safe_fetch_ticker(exchange, <span class="hljs-string">&#x27;BTC/USDT&#x27;</span>)
<span class="hljs-keyword">if</span> ticker:
    <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;BTC/USDT价格: <span class="hljs-subst">{ticker[<span class="hljs-string">&#x27;last&#x27;</span>]}</span>&quot;</span>)
</code></pre>
<h2 id="性能优化建议">性能优化建议</h2>
<h3 id="1-复用交易所实例">1. 复用交易所实例</h3>
<pre><code class="language-javascript"><span class="hljs-comment">// 好的做法</span>
<span class="hljs-keyword">const</span> exchange = <span class="hljs-keyword">new</span> ccxt.<span class="hljs-title function_">binance</span>();
<span class="hljs-keyword">const</span> ticker1 = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">fetchTicker</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>);
<span class="hljs-keyword">const</span> ticker2 = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">fetchTicker</span>(<span class="hljs-string">&#x27;ETH/USDT&#x27;</span>);

<span class="hljs-comment">// 避免这样做</span>
<span class="hljs-keyword">const</span> ticker1 = <span class="hljs-keyword">await</span> (<span class="hljs-keyword">new</span> ccxt.<span class="hljs-title function_">binance</span>()).<span class="hljs-title function_">fetchTicker</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>);
<span class="hljs-keyword">const</span> ticker2 = <span class="hljs-keyword">await</span> (<span class="hljs-keyword">new</span> ccxt.<span class="hljs-title function_">binance</span>()).<span class="hljs-title function_">fetchTicker</span>(<span class="hljs-string">&#x27;ETH/USDT&#x27;</span>);
</code></pre>
<h3 id="2-批量获取数据">2. 批量获取数据</h3>
<pre><code class="language-javascript"><span class="hljs-comment">// 好的做法 - 一次获取多个行情</span>
<span class="hljs-keyword">const</span> tickers = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">fetchTickers</span>([<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, <span class="hljs-string">&#x27;ETH/USDT&#x27;</span>]);

<span class="hljs-comment">// 避免多次单独请求</span>
<span class="hljs-keyword">const</span> btcTicker = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">fetchTicker</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>);
<span class="hljs-keyword">const</span> ethTicker = <span class="hljs-keyword">await</span> exchange.<span class="hljs-title function_">fetchTicker</span>(<span class="hljs-string">&#x27;ETH/USDT&#x27;</span>);
</code></pre>
<h3 id="3-合理设置频率限制">3. 合理设置频率限制</h3>
<pre><code class="language-javascript"><span class="hljs-keyword">const</span> exchange = <span class="hljs-keyword">new</span> ccxt.<span class="hljs-title function_">binance</span>({
    <span class="hljs-string">&#x27;rateLimit&#x27;</span>: <span class="hljs-number">1200</span>,  <span class="hljs-comment">// 根据交易所限制调整</span>
    <span class="hljs-string">&#x27;enableRateLimit&#x27;</span>: <span class="hljs-literal">true</span>,  <span class="hljs-comment">// 启用自动频率限制</span>
});
</code></pre>
<h2 id="常用工具函数">常用工具函数</h2>
<h3 id="价格精度处理">价格精度处理</h3>
<pre><code class="language-javascript"><span class="hljs-comment">// 获取交易对的价格精度</span>
<span class="hljs-keyword">const</span> market = exchange.<span class="hljs-title function_">market</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>);
<span class="hljs-keyword">const</span> pricePrecision = market.<span class="hljs-property">precision</span>.<span class="hljs-property">price</span>;
<span class="hljs-keyword">const</span> amountPrecision = market.<span class="hljs-property">precision</span>.<span class="hljs-property">amount</span>;

<span class="hljs-comment">// 格式化价格</span>
<span class="hljs-keyword">const</span> price = exchange.<span class="hljs-title function_">priceToPrecision</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, <span class="hljs-number">47123.456789</span>);
<span class="hljs-keyword">const</span> amount = exchange.<span class="hljs-title function_">amountToPrecision</span>(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, <span class="hljs-number">1.23456789</span>);
</code></pre>
<h3 id="时间戳转换">时间戳转换</h3>
<pre><code class="language-javascript"><span class="hljs-comment">// 当前时间戳</span>
<span class="hljs-keyword">const</span> now = exchange.<span class="hljs-title function_">milliseconds</span>();

<span class="hljs-comment">// 时间戳转日期字符串</span>
<span class="hljs-keyword">const</span> dateString = exchange.<span class="hljs-title function_">iso8601</span>(now);

<span class="hljs-comment">// 日期字符串转时间戳</span>
<span class="hljs-keyword">const</span> timestamp = exchange.<span class="hljs-title function_">parse8601</span>(<span class="hljs-string">&#x27;2022-01-01T00:00:00Z&#x27;</span>);
</code></pre>
<h3 id="费用计算">费用计算</h3>
<pre><code class="language-javascript"><span class="hljs-comment">// 获取交易费用</span>
<span class="hljs-keyword">const</span> tradingFees = exchange.<span class="hljs-property">fees</span>.<span class="hljs-property">trading</span>;
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">&#x27;Maker费率:&#x27;</span>, tradingFees.<span class="hljs-property">maker</span>);
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">&#x27;Taker费率:&#x27;</span>, tradingFees.<span class="hljs-property">taker</span>);

<span class="hljs-comment">// 计算订单费用</span>
<span class="hljs-keyword">const</span> cost = <span class="hljs-number">1000</span>;  <span class="hljs-comment">// USDT</span>
<span class="hljs-keyword">const</span> fee = cost * tradingFees.<span class="hljs-property">taker</span>;
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">&#x27;预估手续费:&#x27;</span>, fee, <span class="hljs-string">&#x27;USDT&#x27;</span>);
</code></pre>
<h2 id="实战示例">实战示例</h2>
<h3 id="简单的交易机器人">简单的交易机器人</h3>
<pre><code class="language-python"><span class="hljs-keyword">import</span> ccxt
<span class="hljs-keyword">import</span> time

<span class="hljs-keyword">class</span> <span class="hljs-title class_">SimpleBot</span>:
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">__init__</span>(<span class="hljs-params">self, exchange_id, api_key, secret</span>):
        exchange_class = <span class="hljs-built_in">getattr</span>(ccxt, exchange_id)
        self.exchange = exchange_class({
            <span class="hljs-string">&#x27;apiKey&#x27;</span>: api_key,
            <span class="hljs-string">&#x27;secret&#x27;</span>: secret,
            <span class="hljs-string">&#x27;sandbox&#x27;</span>: <span class="hljs-literal">True</span>,  <span class="hljs-comment"># 测试环境</span>
        })

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">get_price</span>(<span class="hljs-params">self, symbol</span>):
        <span class="hljs-string">&quot;&quot;&quot;获取当前价格&quot;&quot;&quot;</span>
        ticker = self.exchange.fetch_ticker(symbol)
        <span class="hljs-keyword">return</span> ticker[<span class="hljs-string">&#x27;last&#x27;</span>]

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">place_buy_order</span>(<span class="hljs-params">self, symbol, amount, price</span>):
        <span class="hljs-string">&quot;&quot;&quot;下买单&quot;&quot;&quot;</span>
        <span class="hljs-keyword">try</span>:
            order = self.exchange.create_limit_buy_order(symbol, amount, price)
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;买单已下达: <span class="hljs-subst">{order[<span class="hljs-string">&#x27;id&#x27;</span>]}</span>&quot;</span>)
            <span class="hljs-keyword">return</span> order
        <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;下单失败: <span class="hljs-subst">{e}</span>&quot;</span>)
            <span class="hljs-keyword">return</span> <span class="hljs-literal">None</span>

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">place_sell_order</span>(<span class="hljs-params">self, symbol, amount, price</span>):
        <span class="hljs-string">&quot;&quot;&quot;下卖单&quot;&quot;&quot;</span>
        <span class="hljs-keyword">try</span>:
            order = self.exchange.create_limit_sell_order(symbol, amount, price)
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;卖单已下达: <span class="hljs-subst">{order[<span class="hljs-string">&#x27;id&#x27;</span>]}</span>&quot;</span>)
            <span class="hljs-keyword">return</span> order
        <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;下单失败: <span class="hljs-subst">{e}</span>&quot;</span>)
            <span class="hljs-keyword">return</span> <span class="hljs-literal">None</span>

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">simple_strategy</span>(<span class="hljs-params">self, symbol, buy_threshold, sell_threshold</span>):
        <span class="hljs-string">&quot;&quot;&quot;简单的买卖策略&quot;&quot;&quot;</span>
        current_price = self.get_price(symbol)
        <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;当前价格: <span class="hljs-subst">{current_price}</span>&quot;</span>)

        <span class="hljs-comment"># 这里可以实现你的交易策略</span>
        <span class="hljs-comment"># 例如：价格低于买入阈值时买入，高于卖出阈值时卖出</span>

        <span class="hljs-keyword">if</span> current_price &lt; buy_threshold:
            self.place_buy_order(symbol, <span class="hljs-number">0.001</span>, current_price * <span class="hljs-number">0.99</span>)
        <span class="hljs-keyword">elif</span> current_price &gt; sell_threshold:
            self.place_sell_order(symbol, <span class="hljs-number">0.001</span>, current_price * <span class="hljs-number">1.01</span>)

<span class="hljs-comment"># 使用示例</span>
bot = SimpleBot(<span class="hljs-string">&#x27;binance&#x27;</span>, <span class="hljs-string">&#x27;your_api_key&#x27;</span>, <span class="hljs-string">&#x27;your_secret&#x27;</span>)
bot.simple_strategy(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>, <span class="hljs-number">45000</span>, <span class="hljs-number">50000</span>)
</code></pre>
<h3 id="套利机器人示例">套利机器人示例</h3>
<pre><code class="language-python"><span class="hljs-keyword">import</span> ccxt
<span class="hljs-keyword">import</span> asyncio

<span class="hljs-keyword">class</span> <span class="hljs-title class_">ArbitrageBot</span>:
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">__init__</span>(<span class="hljs-params">self</span>):
        self.exchanges = {
            <span class="hljs-string">&#x27;binance&#x27;</span>: ccxt.binance(),
            <span class="hljs-string">&#x27;okx&#x27;</span>: ccxt.okx(),
            <span class="hljs-string">&#x27;bybit&#x27;</span>: ccxt.bybit()
        }

    <span class="hljs-keyword">async</span> <span class="hljs-keyword">def</span> <span class="hljs-title function_">get_prices</span>(<span class="hljs-params">self, symbol</span>):
        <span class="hljs-string">&quot;&quot;&quot;获取所有交易所的价格&quot;&quot;&quot;</span>
        prices = {}
        <span class="hljs-keyword">for</span> name, exchange <span class="hljs-keyword">in</span> self.exchanges.items():
            <span class="hljs-keyword">try</span>:
                ticker = <span class="hljs-keyword">await</span> exchange.fetch_ticker(symbol)
                prices[name] = {
                    <span class="hljs-string">&#x27;bid&#x27;</span>: ticker[<span class="hljs-string">&#x27;bid&#x27;</span>],
                    <span class="hljs-string">&#x27;ask&#x27;</span>: ticker[<span class="hljs-string">&#x27;ask&#x27;</span>]
                }
            <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
                <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;获取<span class="hljs-subst">{name}</span>价格失败: <span class="hljs-subst">{e}</span>&quot;</span>)
        <span class="hljs-keyword">return</span> prices

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">find_arbitrage_opportunity</span>(<span class="hljs-params">self, prices</span>):
        <span class="hljs-string">&quot;&quot;&quot;寻找套利机会&quot;&quot;&quot;</span>
        opportunities = []

        <span class="hljs-keyword">for</span> buy_exchange, buy_data <span class="hljs-keyword">in</span> prices.items():
            <span class="hljs-keyword">for</span> sell_exchange, sell_data <span class="hljs-keyword">in</span> prices.items():
                <span class="hljs-keyword">if</span> buy_exchange != sell_exchange:
                    profit_rate = (sell_data[<span class="hljs-string">&#x27;bid&#x27;</span>] - buy_data[<span class="hljs-string">&#x27;ask&#x27;</span>]) / buy_data[<span class="hljs-string">&#x27;ask&#x27;</span>]
                    <span class="hljs-keyword">if</span> profit_rate &gt; <span class="hljs-number">0.005</span>:  <span class="hljs-comment"># 0.5%以上的套利机会</span>
                        opportunities.append({
                            <span class="hljs-string">&#x27;buy_exchange&#x27;</span>: buy_exchange,
                            <span class="hljs-string">&#x27;sell_exchange&#x27;</span>: sell_exchange,
                            <span class="hljs-string">&#x27;buy_price&#x27;</span>: buy_data[<span class="hljs-string">&#x27;ask&#x27;</span>],
                            <span class="hljs-string">&#x27;sell_price&#x27;</span>: sell_data[<span class="hljs-string">&#x27;bid&#x27;</span>],
                            <span class="hljs-string">&#x27;profit_rate&#x27;</span>: profit_rate
                        })

        <span class="hljs-keyword">return</span> opportunities

    <span class="hljs-keyword">async</span> <span class="hljs-keyword">def</span> <span class="hljs-title function_">run</span>(<span class="hljs-params">self, symbol</span>):
        <span class="hljs-string">&quot;&quot;&quot;运行套利检测&quot;&quot;&quot;</span>
        prices = <span class="hljs-keyword">await</span> self.get_prices(symbol)
        opportunities = self.find_arbitrage_opportunity(prices)

        <span class="hljs-keyword">for</span> opp <span class="hljs-keyword">in</span> opportunities:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;套利机会: 在<span class="hljs-subst">{opp[<span class="hljs-string">&#x27;buy_exchange&#x27;</span>]}</span>买入<span class="hljs-subst">{opp[<span class="hljs-string">&#x27;buy_price&#x27;</span>]}</span>, &quot;</span>
                  <span class="hljs-string">f&quot;在<span class="hljs-subst">{opp[<span class="hljs-string">&#x27;sell_exchange&#x27;</span>]}</span>卖出<span class="hljs-subst">{opp[<span class="hljs-string">&#x27;sell_price&#x27;</span>]}</span>, &quot;</span>
                  <span class="hljs-string">f&quot;利润率: <span class="hljs-subst">{opp[<span class="hljs-string">&#x27;profit_rate&#x27;</span>]:<span class="hljs-number">.2</span>%}</span>&quot;</span>)

<span class="hljs-comment"># 使用示例</span>
<span class="hljs-comment"># bot = ArbitrageBot()</span>
<span class="hljs-comment"># asyncio.run(bot.run(&#x27;BTC/USDT&#x27;))</span>
</code></pre>
<h3 id="网格交易策略">网格交易策略</h3>
<pre><code class="language-python"><span class="hljs-keyword">import</span> ccxt
<span class="hljs-keyword">import</span> numpy <span class="hljs-keyword">as</span> np

<span class="hljs-keyword">class</span> <span class="hljs-title class_">GridTradingBot</span>:
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">__init__</span>(<span class="hljs-params">self, exchange, symbol, grid_size=<span class="hljs-number">10</span>, grid_spacing=<span class="hljs-number">0.01</span></span>):
        self.exchange = exchange
        self.symbol = symbol
        self.grid_size = grid_size
        self.grid_spacing = grid_spacing
        self.orders = {}

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">create_grid</span>(<span class="hljs-params">self, center_price</span>):
        <span class="hljs-string">&quot;&quot;&quot;创建网格订单&quot;&quot;&quot;</span>
        buy_orders = []
        sell_orders = []

        <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> <span class="hljs-built_in">range</span>(<span class="hljs-number">1</span>, self.grid_size + <span class="hljs-number">1</span>):
            <span class="hljs-comment"># 买单价格</span>
            buy_price = center_price * (<span class="hljs-number">1</span> - self.grid_spacing * i)
            <span class="hljs-comment"># 卖单价格</span>
            sell_price = center_price * (<span class="hljs-number">1</span> + self.grid_spacing * i)

            buy_orders.append(buy_price)
            sell_orders.append(sell_price)

        <span class="hljs-keyword">return</span> buy_orders, sell_orders

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">place_grid_orders</span>(<span class="hljs-params">self, buy_prices, sell_prices, amount</span>):
        <span class="hljs-string">&quot;&quot;&quot;下网格订单&quot;&quot;&quot;</span>
        <span class="hljs-keyword">for</span> price <span class="hljs-keyword">in</span> buy_prices:
            <span class="hljs-keyword">try</span>:
                order = self.exchange.create_limit_buy_order(
                    self.symbol, amount, price
                )
                self.orders[order[<span class="hljs-string">&#x27;id&#x27;</span>]] = order
                <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;买单已下达: <span class="hljs-subst">{price}</span>&quot;</span>)
            <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
                <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;买单下达失败: <span class="hljs-subst">{e}</span>&quot;</span>)

        <span class="hljs-keyword">for</span> price <span class="hljs-keyword">in</span> sell_prices:
            <span class="hljs-keyword">try</span>:
                order = self.exchange.create_limit_sell_order(
                    self.symbol, amount, price
                )
                self.orders[order[<span class="hljs-string">&#x27;id&#x27;</span>]] = order
                <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;卖单已下达: <span class="hljs-subst">{price}</span>&quot;</span>)
            <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
                <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;卖单下达失败: <span class="hljs-subst">{e}</span>&quot;</span>)

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">check_filled_orders</span>(<span class="hljs-params">self</span>):
        <span class="hljs-string">&quot;&quot;&quot;检查已成交订单&quot;&quot;&quot;</span>
        <span class="hljs-keyword">for</span> order_id <span class="hljs-keyword">in</span> <span class="hljs-built_in">list</span>(self.orders.keys()):
            <span class="hljs-keyword">try</span>:
                order = self.exchange.fetch_order(order_id, self.symbol)
                <span class="hljs-keyword">if</span> order[<span class="hljs-string">&#x27;status&#x27;</span>] == <span class="hljs-string">&#x27;closed&#x27;</span>:
                    <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;订单<span class="hljs-subst">{order_id}</span>已成交&quot;</span>)
                    <span class="hljs-keyword">del</span> self.orders[order_id]
                    <span class="hljs-comment"># 在这里可以添加重新下单的逻辑</span>
            <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
                <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;检查订单状态失败: <span class="hljs-subst">{e}</span>&quot;</span>)

<span class="hljs-comment"># 使用示例</span>
<span class="hljs-comment"># exchange = ccxt.binance({&#x27;apiKey&#x27;: &#x27;key&#x27;, &#x27;secret&#x27;: &#x27;secret&#x27;})</span>
<span class="hljs-comment"># bot = GridTradingBot(exchange, &#x27;BTC/USDT&#x27;)</span>
<span class="hljs-comment"># current_price = exchange.fetch_ticker(&#x27;BTC/USDT&#x27;)[&#x27;last&#x27;]</span>
<span class="hljs-comment"># buy_prices, sell_prices = bot.create_grid(current_price)</span>
<span class="hljs-comment"># bot.place_grid_orders(buy_prices, sell_prices, 0.001)</span>
</code></pre>
<h2 id="风险管理">风险管理</h2>
<h3 id="止损止盈">止损止盈</h3>
<pre><code class="language-python"><span class="hljs-keyword">class</span> <span class="hljs-title class_">RiskManager</span>:
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">__init__</span>(<span class="hljs-params">self, exchange</span>):
        self.exchange = exchange

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">set_stop_loss</span>(<span class="hljs-params">self, symbol, position_size, entry_price, stop_loss_pct=<span class="hljs-number">0.05</span></span>):
        <span class="hljs-string">&quot;&quot;&quot;设置止损&quot;&quot;&quot;</span>
        stop_price = entry_price * (<span class="hljs-number">1</span> - stop_loss_pct)

        <span class="hljs-keyword">try</span>:
            order = self.exchange.create_stop_market_order(
                symbol, <span class="hljs-string">&#x27;sell&#x27;</span>, position_size, stop_price
            )
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;止损单已设置: <span class="hljs-subst">{stop_price}</span>&quot;</span>)
            <span class="hljs-keyword">return</span> order
        <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;设置止损失败: <span class="hljs-subst">{e}</span>&quot;</span>)
            <span class="hljs-keyword">return</span> <span class="hljs-literal">None</span>

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">set_take_profit</span>(<span class="hljs-params">self, symbol, position_size, entry_price, take_profit_pct=<span class="hljs-number">0.10</span></span>):
        <span class="hljs-string">&quot;&quot;&quot;设置止盈&quot;&quot;&quot;</span>
        take_profit_price = entry_price * (<span class="hljs-number">1</span> + take_profit_pct)

        <span class="hljs-keyword">try</span>:
            order = self.exchange.create_limit_sell_order(
                symbol, position_size, take_profit_price
            )
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;止盈单已设置: <span class="hljs-subst">{take_profit_price}</span>&quot;</span>)
            <span class="hljs-keyword">return</span> order
        <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;设置止盈失败: <span class="hljs-subst">{e}</span>&quot;</span>)
            <span class="hljs-keyword">return</span> <span class="hljs-literal">None</span>

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">calculate_position_size</span>(<span class="hljs-params">self, account_balance, risk_pct=<span class="hljs-number">0.02</span>, entry_price=<span class="hljs-literal">None</span>, stop_price=<span class="hljs-literal">None</span></span>):
        <span class="hljs-string">&quot;&quot;&quot;计算仓位大小&quot;&quot;&quot;</span>
        <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> entry_price <span class="hljs-keyword">or</span> <span class="hljs-keyword">not</span> stop_price:
            <span class="hljs-keyword">return</span> <span class="hljs-literal">None</span>

        risk_amount = account_balance * risk_pct
        price_diff = <span class="hljs-built_in">abs</span>(entry_price - stop_price)
        position_size = risk_amount / price_diff

        <span class="hljs-keyword">return</span> position_size
</code></pre>
<h3 id="资金管理">资金管理</h3>
<pre><code class="language-python"><span class="hljs-keyword">class</span> <span class="hljs-title class_">MoneyManager</span>:
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">__init__</span>(<span class="hljs-params">self, exchange</span>):
        self.exchange = exchange

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">get_account_value</span>(<span class="hljs-params">self</span>):
        <span class="hljs-string">&quot;&quot;&quot;获取账户总价值（以USDT计算）&quot;&quot;&quot;</span>
        balance = self.exchange.fetch_balance()
        total_value = <span class="hljs-number">0</span>

        <span class="hljs-keyword">for</span> currency, amount <span class="hljs-keyword">in</span> balance[<span class="hljs-string">&#x27;total&#x27;</span>].items():
            <span class="hljs-keyword">if</span> currency == <span class="hljs-string">&#x27;USDT&#x27;</span>:
                total_value += amount
            <span class="hljs-keyword">elif</span> amount &gt; <span class="hljs-number">0</span>:
                <span class="hljs-keyword">try</span>:
                    ticker = self.exchange.fetch_ticker(<span class="hljs-string">f&#x27;<span class="hljs-subst">{currency}</span>/USDT&#x27;</span>)
                    total_value += amount * ticker[<span class="hljs-string">&#x27;last&#x27;</span>]
                <span class="hljs-keyword">except</span>:
                    <span class="hljs-keyword">pass</span>  <span class="hljs-comment"># 忽略无法获取价格的币种</span>

        <span class="hljs-keyword">return</span> total_value

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">calculate_max_position_size</span>(<span class="hljs-params">self, symbol, max_risk_pct=<span class="hljs-number">0.1</span></span>):
        <span class="hljs-string">&quot;&quot;&quot;计算最大仓位大小&quot;&quot;&quot;</span>
        account_value = self.get_account_value()
        max_position_value = account_value * max_risk_pct

        ticker = self.exchange.fetch_ticker(symbol)
        current_price = ticker[<span class="hljs-string">&#x27;last&#x27;</span>]

        max_position_size = max_position_value / current_price
        <span class="hljs-keyword">return</span> max_position_size

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">diversify_portfolio</span>(<span class="hljs-params">self, symbols, total_amount</span>):
        <span class="hljs-string">&quot;&quot;&quot;分散投资组合&quot;&quot;&quot;</span>
        amount_per_symbol = total_amount / <span class="hljs-built_in">len</span>(symbols)
        allocations = {}

        <span class="hljs-keyword">for</span> symbol <span class="hljs-keyword">in</span> symbols:
            ticker = self.exchange.fetch_ticker(symbol)
            price = ticker[<span class="hljs-string">&#x27;last&#x27;</span>]
            size = amount_per_symbol / price
            allocations[symbol] = size

        <span class="hljs-keyword">return</span> allocations
</code></pre>
<h2 id="数据分析工具">数据分析工具</h2>
<h3 id="技术指标计算">技术指标计算</h3>
<pre><code class="language-python"><span class="hljs-keyword">import</span> pandas <span class="hljs-keyword">as</span> pd
<span class="hljs-keyword">import</span> numpy <span class="hljs-keyword">as</span> np

<span class="hljs-keyword">class</span> <span class="hljs-title class_">TechnicalAnalysis</span>:
<span class="hljs-meta">    @staticmethod</span>
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">sma</span>(<span class="hljs-params">data, period</span>):
        <span class="hljs-string">&quot;&quot;&quot;简单移动平均线&quot;&quot;&quot;</span>
        <span class="hljs-keyword">return</span> data.rolling(window=period).mean()

<span class="hljs-meta">    @staticmethod</span>
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">ema</span>(<span class="hljs-params">data, period</span>):
        <span class="hljs-string">&quot;&quot;&quot;指数移动平均线&quot;&quot;&quot;</span>
        <span class="hljs-keyword">return</span> data.ewm(span=period).mean()

<span class="hljs-meta">    @staticmethod</span>
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">rsi</span>(<span class="hljs-params">data, period=<span class="hljs-number">14</span></span>):
        <span class="hljs-string">&quot;&quot;&quot;相对强弱指数&quot;&quot;&quot;</span>
        delta = data.diff()
        gain = (delta.where(delta &gt; <span class="hljs-number">0</span>, <span class="hljs-number">0</span>)).rolling(window=period).mean()
        loss = (-delta.where(delta &lt; <span class="hljs-number">0</span>, <span class="hljs-number">0</span>)).rolling(window=period).mean()
        rs = gain / loss
        rsi = <span class="hljs-number">100</span> - (<span class="hljs-number">100</span> / (<span class="hljs-number">1</span> + rs))
        <span class="hljs-keyword">return</span> rsi

<span class="hljs-meta">    @staticmethod</span>
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">bollinger_bands</span>(<span class="hljs-params">data, period=<span class="hljs-number">20</span>, std_dev=<span class="hljs-number">2</span></span>):
        <span class="hljs-string">&quot;&quot;&quot;布林带&quot;&quot;&quot;</span>
        sma = data.rolling(window=period).mean()
        std = data.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        <span class="hljs-keyword">return</span> upper_band, sma, lower_band

<span class="hljs-meta">    @staticmethod</span>
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">macd</span>(<span class="hljs-params">data, fast=<span class="hljs-number">12</span>, slow=<span class="hljs-number">26</span>, signal=<span class="hljs-number">9</span></span>):
        <span class="hljs-string">&quot;&quot;&quot;MACD指标&quot;&quot;&quot;</span>
        ema_fast = data.ewm(span=fast).mean()
        ema_slow = data.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        <span class="hljs-keyword">return</span> macd_line, signal_line, histogram

<span class="hljs-comment"># 使用示例</span>
<span class="hljs-keyword">def</span> <span class="hljs-title function_">analyze_symbol</span>(<span class="hljs-params">exchange, symbol, timeframe=<span class="hljs-string">&#x27;1h&#x27;</span>, limit=<span class="hljs-number">100</span></span>):
    <span class="hljs-string">&quot;&quot;&quot;分析交易对&quot;&quot;&quot;</span>
    <span class="hljs-comment"># 获取K线数据</span>
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
    df = pd.DataFrame(ohlcv, columns=[<span class="hljs-string">&#x27;timestamp&#x27;</span>, <span class="hljs-string">&#x27;open&#x27;</span>, <span class="hljs-string">&#x27;high&#x27;</span>, <span class="hljs-string">&#x27;low&#x27;</span>, <span class="hljs-string">&#x27;close&#x27;</span>, <span class="hljs-string">&#x27;volume&#x27;</span>])
    df[<span class="hljs-string">&#x27;timestamp&#x27;</span>] = pd.to_datetime(df[<span class="hljs-string">&#x27;timestamp&#x27;</span>], unit=<span class="hljs-string">&#x27;ms&#x27;</span>)

    <span class="hljs-comment"># 计算技术指标</span>
    df[<span class="hljs-string">&#x27;sma_20&#x27;</span>] = TechnicalAnalysis.sma(df[<span class="hljs-string">&#x27;close&#x27;</span>], <span class="hljs-number">20</span>)
    df[<span class="hljs-string">&#x27;ema_12&#x27;</span>] = TechnicalAnalysis.ema(df[<span class="hljs-string">&#x27;close&#x27;</span>], <span class="hljs-number">12</span>)
    df[<span class="hljs-string">&#x27;rsi&#x27;</span>] = TechnicalAnalysis.rsi(df[<span class="hljs-string">&#x27;close&#x27;</span>])

    upper, middle, lower = TechnicalAnalysis.bollinger_bands(df[<span class="hljs-string">&#x27;close&#x27;</span>])
    df[<span class="hljs-string">&#x27;bb_upper&#x27;</span>] = upper
    df[<span class="hljs-string">&#x27;bb_middle&#x27;</span>] = middle
    df[<span class="hljs-string">&#x27;bb_lower&#x27;</span>] = lower

    macd, signal, histogram = TechnicalAnalysis.macd(df[<span class="hljs-string">&#x27;close&#x27;</span>])
    df[<span class="hljs-string">&#x27;macd&#x27;</span>] = macd
    df[<span class="hljs-string">&#x27;macd_signal&#x27;</span>] = signal
    df[<span class="hljs-string">&#x27;macd_histogram&#x27;</span>] = histogram

    <span class="hljs-keyword">return</span> df

<span class="hljs-comment"># exchange = ccxt.binance()</span>
<span class="hljs-comment"># analysis = analyze_symbol(exchange, &#x27;BTC/USDT&#x27;)</span>
<span class="hljs-comment"># print(analysis.tail())</span>
</code></pre>
<h2 id="监控和日志">监控和日志</h2>
<h3 id="交易日志记录">交易日志记录</h3>
<pre><code class="language-python"><span class="hljs-keyword">import</span> logging
<span class="hljs-keyword">from</span> datetime <span class="hljs-keyword">import</span> datetime
<span class="hljs-keyword">import</span> json

<span class="hljs-keyword">class</span> <span class="hljs-title class_">TradingLogger</span>:
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">__init__</span>(<span class="hljs-params">self, log_file=<span class="hljs-string">&#x27;trading.log&#x27;</span></span>):
        self.logger = logging.getLogger(<span class="hljs-string">&#x27;TradingBot&#x27;</span>)
        self.logger.setLevel(logging.INFO)

        <span class="hljs-comment"># 文件处理器</span>
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)

        <span class="hljs-comment"># 控制台处理器</span>
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        <span class="hljs-comment"># 格式化器</span>
        formatter = logging.Formatter(
            <span class="hljs-string">&#x27;%(asctime)s - %(name)s - %(levelname)s - %(message)s&#x27;</span>
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">log_order</span>(<span class="hljs-params">self, action, order</span>):
        <span class="hljs-string">&quot;&quot;&quot;记录订单信息&quot;&quot;&quot;</span>
        log_data = {
            <span class="hljs-string">&#x27;action&#x27;</span>: action,
            <span class="hljs-string">&#x27;order_id&#x27;</span>: order.get(<span class="hljs-string">&#x27;id&#x27;</span>),
            <span class="hljs-string">&#x27;symbol&#x27;</span>: order.get(<span class="hljs-string">&#x27;symbol&#x27;</span>),
            <span class="hljs-string">&#x27;type&#x27;</span>: order.get(<span class="hljs-string">&#x27;type&#x27;</span>),
            <span class="hljs-string">&#x27;side&#x27;</span>: order.get(<span class="hljs-string">&#x27;side&#x27;</span>),
            <span class="hljs-string">&#x27;amount&#x27;</span>: order.get(<span class="hljs-string">&#x27;amount&#x27;</span>),
            <span class="hljs-string">&#x27;price&#x27;</span>: order.get(<span class="hljs-string">&#x27;price&#x27;</span>),
            <span class="hljs-string">&#x27;status&#x27;</span>: order.get(<span class="hljs-string">&#x27;status&#x27;</span>),
            <span class="hljs-string">&#x27;timestamp&#x27;</span>: datetime.now().isoformat()
        }
        self.logger.info(<span class="hljs-string">f&quot;ORDER_<span class="hljs-subst">{action.upper()}</span>: <span class="hljs-subst">{json.dumps(log_data)}</span>&quot;</span>)

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">log_trade</span>(<span class="hljs-params">self, trade</span>):
        <span class="hljs-string">&quot;&quot;&quot;记录交易信息&quot;&quot;&quot;</span>
        log_data = {
            <span class="hljs-string">&#x27;trade_id&#x27;</span>: trade.get(<span class="hljs-string">&#x27;id&#x27;</span>),
            <span class="hljs-string">&#x27;symbol&#x27;</span>: trade.get(<span class="hljs-string">&#x27;symbol&#x27;</span>),
            <span class="hljs-string">&#x27;side&#x27;</span>: trade.get(<span class="hljs-string">&#x27;side&#x27;</span>),
            <span class="hljs-string">&#x27;amount&#x27;</span>: trade.get(<span class="hljs-string">&#x27;amount&#x27;</span>),
            <span class="hljs-string">&#x27;price&#x27;</span>: trade.get(<span class="hljs-string">&#x27;price&#x27;</span>),
            <span class="hljs-string">&#x27;cost&#x27;</span>: trade.get(<span class="hljs-string">&#x27;cost&#x27;</span>),
            <span class="hljs-string">&#x27;fee&#x27;</span>: trade.get(<span class="hljs-string">&#x27;fee&#x27;</span>),
            <span class="hljs-string">&#x27;timestamp&#x27;</span>: datetime.now().isoformat()
        }
        self.logger.info(<span class="hljs-string">f&quot;TRADE: <span class="hljs-subst">{json.dumps(log_data)}</span>&quot;</span>)

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">log_error</span>(<span class="hljs-params">self, error, context=<span class="hljs-literal">None</span></span>):
        <span class="hljs-string">&quot;&quot;&quot;记录错误信息&quot;&quot;&quot;</span>
        error_data = {
            <span class="hljs-string">&#x27;error&#x27;</span>: <span class="hljs-built_in">str</span>(error),
            <span class="hljs-string">&#x27;context&#x27;</span>: context,
            <span class="hljs-string">&#x27;timestamp&#x27;</span>: datetime.now().isoformat()
        }
        self.logger.error(<span class="hljs-string">f&quot;ERROR: <span class="hljs-subst">{json.dumps(error_data)}</span>&quot;</span>)

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">log_balance</span>(<span class="hljs-params">self, balance</span>):
        <span class="hljs-string">&quot;&quot;&quot;记录余额信息&quot;&quot;&quot;</span>
        self.logger.info(<span class="hljs-string">f&quot;BALANCE: <span class="hljs-subst">{json.dumps(balance[<span class="hljs-string">&#x27;total&#x27;</span>])}</span>&quot;</span>)

<span class="hljs-comment"># 使用示例</span>
logger = TradingLogger()

<span class="hljs-comment"># 记录订单</span>
<span class="hljs-comment"># order = exchange.create_limit_buy_order(&#x27;BTC/USDT&#x27;, 0.001, 45000)</span>
<span class="hljs-comment"># logger.log_order(&#x27;create&#x27;, order)</span>
</code></pre>
<h3 id="性能监控">性能监控</h3>
<pre><code class="language-python"><span class="hljs-keyword">import</span> time
<span class="hljs-keyword">from</span> functools <span class="hljs-keyword">import</span> wraps

<span class="hljs-keyword">class</span> <span class="hljs-title class_">PerformanceMonitor</span>:
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">__init__</span>(<span class="hljs-params">self</span>):
        self.metrics = {}

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">time_function</span>(<span class="hljs-params">self, func_name</span>):
        <span class="hljs-string">&quot;&quot;&quot;装饰器：测量函数执行时间&quot;&quot;&quot;</span>
        <span class="hljs-keyword">def</span> <span class="hljs-title function_">decorator</span>(<span class="hljs-params">func</span>):
<span class="hljs-meta">            @wraps(<span class="hljs-params">func</span>)</span>
            <span class="hljs-keyword">def</span> <span class="hljs-title function_">wrapper</span>(<span class="hljs-params">*args, **kwargs</span>):
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()

                execution_time = end_time - start_time
                <span class="hljs-keyword">if</span> func_name <span class="hljs-keyword">not</span> <span class="hljs-keyword">in</span> self.metrics:
                    self.metrics[func_name] = []
                self.metrics[func_name].append(execution_time)

                <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;<span class="hljs-subst">{func_name}</span> 执行时间: <span class="hljs-subst">{execution_time:<span class="hljs-number">.4</span>f}</span>秒&quot;</span>)
                <span class="hljs-keyword">return</span> result
            <span class="hljs-keyword">return</span> wrapper
        <span class="hljs-keyword">return</span> decorator

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">get_average_time</span>(<span class="hljs-params">self, func_name</span>):
        <span class="hljs-string">&quot;&quot;&quot;获取函数平均执行时间&quot;&quot;&quot;</span>
        <span class="hljs-keyword">if</span> func_name <span class="hljs-keyword">in</span> self.metrics:
            <span class="hljs-keyword">return</span> <span class="hljs-built_in">sum</span>(self.metrics[func_name]) / <span class="hljs-built_in">len</span>(self.metrics[func_name])
        <span class="hljs-keyword">return</span> <span class="hljs-number">0</span>

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">print_stats</span>(<span class="hljs-params">self</span>):
        <span class="hljs-string">&quot;&quot;&quot;打印性能统计&quot;&quot;&quot;</span>
        <span class="hljs-keyword">for</span> func_name, times <span class="hljs-keyword">in</span> self.metrics.items():
            avg_time = <span class="hljs-built_in">sum</span>(times) / <span class="hljs-built_in">len</span>(times)
            max_time = <span class="hljs-built_in">max</span>(times)
            min_time = <span class="hljs-built_in">min</span>(times)
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;<span class="hljs-subst">{func_name}</span>:&quot;</span>)
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;  平均时间: <span class="hljs-subst">{avg_time:<span class="hljs-number">.4</span>f}</span>秒&quot;</span>)
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;  最大时间: <span class="hljs-subst">{max_time:<span class="hljs-number">.4</span>f}</span>秒&quot;</span>)
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;  最小时间: <span class="hljs-subst">{min_time:<span class="hljs-number">.4</span>f}</span>秒&quot;</span>)
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;  调用次数: <span class="hljs-subst">{<span class="hljs-built_in">len</span>(times)}</span>&quot;</span>)

<span class="hljs-comment"># 使用示例</span>
monitor = PerformanceMonitor()

<span class="hljs-meta">@monitor.time_function(<span class="hljs-params"><span class="hljs-string">&#x27;fetch_ticker&#x27;</span></span>)</span>
<span class="hljs-keyword">def</span> <span class="hljs-title function_">fetch_ticker_with_monitoring</span>(<span class="hljs-params">exchange, symbol</span>):
    <span class="hljs-keyword">return</span> exchange.fetch_ticker(symbol)

<span class="hljs-comment"># exchange = ccxt.binance()</span>
<span class="hljs-comment"># ticker = fetch_ticker_with_monitoring(exchange, &#x27;BTC/USDT&#x27;)</span>
<span class="hljs-comment"># monitor.print_stats()</span>
</code></pre>
<h2 id="部署和生产环境">部署和生产环境</h2>
<h3 id="docker部署">Docker部署</h3>
<p>创建Dockerfile：</p>
<pre><code class="language-dockerfile"><span class="hljs-keyword">FROM</span> python:<span class="hljs-number">3.9</span>-slim

<span class="hljs-keyword">WORKDIR</span><span class="language-bash"> /app</span>

<span class="hljs-comment"># 安装依赖</span>
<span class="hljs-keyword">COPY</span><span class="language-bash"> requirements.txt .</span>
<span class="hljs-keyword">RUN</span><span class="language-bash"> pip install -r requirements.txt</span>

<span class="hljs-comment"># 复制代码</span>
<span class="hljs-keyword">COPY</span><span class="language-bash"> . .</span>

<span class="hljs-comment"># 设置环境变量</span>
<span class="hljs-keyword">ENV</span> PYTHONPATH=/app

<span class="hljs-comment"># 运行应用</span>
<span class="hljs-keyword">CMD</span><span class="language-bash"> [<span class="hljs-string">&quot;python&quot;</span>, <span class="hljs-string">&quot;main.py&quot;</span>]</span>
</code></pre>
<p>requirements.txt：</p>
<pre><code>ccxt&gt;=4.4.0
pandas&gt;=1.3.0
numpy&gt;=1.21.0
python-dotenv&gt;=0.19.0
</code></pre>
<p>docker-compose.yml：</p>
<pre><code class="language-yaml"><span class="hljs-attr">version:</span> <span class="hljs-string">&#x27;3.8&#x27;</span>
<span class="hljs-attr">services:</span>
  <span class="hljs-attr">trading-bot:</span>
    <span class="hljs-attr">build:</span> <span class="hljs-string">.</span>
    <span class="hljs-attr">environment:</span>
      <span class="hljs-bullet">-</span> <span class="hljs-string">API_KEY=${API_KEY}</span>
      <span class="hljs-bullet">-</span> <span class="hljs-string">API_SECRET=${API_SECRET}</span>
      <span class="hljs-bullet">-</span> <span class="hljs-string">ENVIRONMENT=production</span>
    <span class="hljs-attr">volumes:</span>
      <span class="hljs-bullet">-</span> <span class="hljs-string">./logs:/app/logs</span>
      <span class="hljs-bullet">-</span> <span class="hljs-string">./data:/app/data</span>
    <span class="hljs-attr">restart:</span> <span class="hljs-string">unless-stopped</span>
</code></pre>
<h3 id="环境变量管理">环境变量管理</h3>
<pre><code class="language-python"><span class="hljs-keyword">import</span> os
<span class="hljs-keyword">from</span> dotenv <span class="hljs-keyword">import</span> load_dotenv

load_dotenv()

<span class="hljs-keyword">class</span> <span class="hljs-title class_">Config</span>:
    <span class="hljs-comment"># API配置</span>
    API_KEY = os.getenv(<span class="hljs-string">&#x27;API_KEY&#x27;</span>)
    API_SECRET = os.getenv(<span class="hljs-string">&#x27;API_SECRET&#x27;</span>)
    API_PASSPHRASE = os.getenv(<span class="hljs-string">&#x27;API_PASSPHRASE&#x27;</span>)  <span class="hljs-comment"># OKX等需要</span>

    <span class="hljs-comment"># 交易所配置</span>
    EXCHANGE = os.getenv(<span class="hljs-string">&#x27;EXCHANGE&#x27;</span>, <span class="hljs-string">&#x27;binance&#x27;</span>)
    SANDBOX = os.getenv(<span class="hljs-string">&#x27;SANDBOX&#x27;</span>, <span class="hljs-string">&#x27;false&#x27;</span>).lower() == <span class="hljs-string">&#x27;true&#x27;</span>

    <span class="hljs-comment"># 交易配置</span>
    SYMBOLS = os.getenv(<span class="hljs-string">&#x27;SYMBOLS&#x27;</span>, <span class="hljs-string">&#x27;BTC/USDT,ETH/USDT&#x27;</span>).split(<span class="hljs-string">&#x27;,&#x27;</span>)
    MAX_POSITION_SIZE = <span class="hljs-built_in">float</span>(os.getenv(<span class="hljs-string">&#x27;MAX_POSITION_SIZE&#x27;</span>, <span class="hljs-string">&#x27;0.1&#x27;</span>))
    RISK_PERCENTAGE = <span class="hljs-built_in">float</span>(os.getenv(<span class="hljs-string">&#x27;RISK_PERCENTAGE&#x27;</span>, <span class="hljs-string">&#x27;0.02&#x27;</span>))

    <span class="hljs-comment"># 日志配置</span>
    LOG_LEVEL = os.getenv(<span class="hljs-string">&#x27;LOG_LEVEL&#x27;</span>, <span class="hljs-string">&#x27;INFO&#x27;</span>)
    LOG_FILE = os.getenv(<span class="hljs-string">&#x27;LOG_FILE&#x27;</span>, <span class="hljs-string">&#x27;trading.log&#x27;</span>)

    <span class="hljs-comment"># 数据库配置（如果使用）</span>
    DATABASE_URL = os.getenv(<span class="hljs-string">&#x27;DATABASE_URL&#x27;</span>)

<span class="hljs-comment"># 使用配置</span>
config = Config()
exchange = <span class="hljs-built_in">getattr</span>(ccxt, config.EXCHANGE)({
    <span class="hljs-string">&#x27;apiKey&#x27;</span>: config.API_KEY,
    <span class="hljs-string">&#x27;secret&#x27;</span>: config.API_SECRET,
    <span class="hljs-string">&#x27;sandbox&#x27;</span>: config.SANDBOX,
})
</code></pre>
<h3 id="数据持久化">数据持久化</h3>
<pre><code class="language-python"><span class="hljs-keyword">import</span> sqlite3
<span class="hljs-keyword">import</span> json
<span class="hljs-keyword">from</span> datetime <span class="hljs-keyword">import</span> datetime

<span class="hljs-keyword">class</span> <span class="hljs-title class_">DatabaseManager</span>:
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">__init__</span>(<span class="hljs-params">self, db_path=<span class="hljs-string">&#x27;trading.db&#x27;</span></span>):
        self.db_path = db_path
        self.init_database()

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">init_database</span>(<span class="hljs-params">self</span>):
        <span class="hljs-string">&quot;&quot;&quot;初始化数据库表&quot;&quot;&quot;</span>
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        <span class="hljs-comment"># 订单表</span>
        cursor.execute(<span class="hljs-string">&#x27;&#x27;&#x27;
            CREATE TABLE IF NOT EXISTS orders (
                id TEXT PRIMARY KEY,
                symbol TEXT,
                type TEXT,
                side TEXT,
                amount REAL,
                price REAL,
                status TEXT,
                timestamp INTEGER,
                raw_data TEXT
            )
        &#x27;&#x27;&#x27;</span>)

        <span class="hljs-comment"># 交易表</span>
        cursor.execute(<span class="hljs-string">&#x27;&#x27;&#x27;
            CREATE TABLE IF NOT EXISTS trades (
                id TEXT PRIMARY KEY,
                order_id TEXT,
                symbol TEXT,
                side TEXT,
                amount REAL,
                price REAL,
                cost REAL,
                fee_cost REAL,
                fee_currency TEXT,
                timestamp INTEGER,
                raw_data TEXT
            )
        &#x27;&#x27;&#x27;</span>)

        <span class="hljs-comment"># 余额历史表</span>
        cursor.execute(<span class="hljs-string">&#x27;&#x27;&#x27;
            CREATE TABLE IF NOT EXISTS balance_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp INTEGER,
                total_value REAL,
                balances TEXT
            )
        &#x27;&#x27;&#x27;</span>)

        conn.commit()
        conn.close()

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">save_order</span>(<span class="hljs-params">self, order</span>):
        <span class="hljs-string">&quot;&quot;&quot;保存订单&quot;&quot;&quot;</span>
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(<span class="hljs-string">&#x27;&#x27;&#x27;
            INSERT OR REPLACE INTO orders
            (id, symbol, type, side, amount, price, status, timestamp, raw_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        &#x27;&#x27;&#x27;</span>, (
            order[<span class="hljs-string">&#x27;id&#x27;</span>],
            order[<span class="hljs-string">&#x27;symbol&#x27;</span>],
            order[<span class="hljs-string">&#x27;type&#x27;</span>],
            order[<span class="hljs-string">&#x27;side&#x27;</span>],
            order[<span class="hljs-string">&#x27;amount&#x27;</span>],
            order[<span class="hljs-string">&#x27;price&#x27;</span>],
            order[<span class="hljs-string">&#x27;status&#x27;</span>],
            order[<span class="hljs-string">&#x27;timestamp&#x27;</span>],
            json.dumps(order)
        ))

        conn.commit()
        conn.close()

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">save_trade</span>(<span class="hljs-params">self, trade</span>):
        <span class="hljs-string">&quot;&quot;&quot;保存交易&quot;&quot;&quot;</span>
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        fee = trade.get(<span class="hljs-string">&#x27;fee&#x27;</span>, {})
        cursor.execute(<span class="hljs-string">&#x27;&#x27;&#x27;
            INSERT OR REPLACE INTO trades
            (id, order_id, symbol, side, amount, price, cost, fee_cost, fee_currency, timestamp, raw_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        &#x27;&#x27;&#x27;</span>, (
            trade[<span class="hljs-string">&#x27;id&#x27;</span>],
            trade.get(<span class="hljs-string">&#x27;order&#x27;</span>),
            trade[<span class="hljs-string">&#x27;symbol&#x27;</span>],
            trade[<span class="hljs-string">&#x27;side&#x27;</span>],
            trade[<span class="hljs-string">&#x27;amount&#x27;</span>],
            trade[<span class="hljs-string">&#x27;price&#x27;</span>],
            trade[<span class="hljs-string">&#x27;cost&#x27;</span>],
            fee.get(<span class="hljs-string">&#x27;cost&#x27;</span>),
            fee.get(<span class="hljs-string">&#x27;currency&#x27;</span>),
            trade[<span class="hljs-string">&#x27;timestamp&#x27;</span>],
            json.dumps(trade)
        ))

        conn.commit()
        conn.close()

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">save_balance_snapshot</span>(<span class="hljs-params">self, balance, total_value</span>):
        <span class="hljs-string">&quot;&quot;&quot;保存余额快照&quot;&quot;&quot;</span>
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(<span class="hljs-string">&#x27;&#x27;&#x27;
            INSERT INTO balance_history (timestamp, total_value, balances)
            VALUES (?, ?, ?)
        &#x27;&#x27;&#x27;</span>, (
            <span class="hljs-built_in">int</span>(datetime.now().timestamp() * <span class="hljs-number">1000</span>),
            total_value,
            json.dumps(balance)
        ))

        conn.commit()
        conn.close()

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">get_trading_stats</span>(<span class="hljs-params">self, days=<span class="hljs-number">30</span></span>):
        <span class="hljs-string">&quot;&quot;&quot;获取交易统计&quot;&quot;&quot;</span>
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        since = <span class="hljs-built_in">int</span>((datetime.now().timestamp() - days * <span class="hljs-number">86400</span>) * <span class="hljs-number">1000</span>)

        cursor.execute(<span class="hljs-string">&#x27;&#x27;&#x27;
            SELECT
                COUNT(*) as trade_count,
                SUM(cost) as total_volume,
                SUM(fee_cost) as total_fees,
                AVG(price) as avg_price
            FROM trades
            WHERE timestamp &gt; ?
        &#x27;&#x27;&#x27;</span>, (since,))

        stats = cursor.fetchone()
        conn.close()

        <span class="hljs-keyword">return</span> {
            <span class="hljs-string">&#x27;trade_count&#x27;</span>: stats[<span class="hljs-number">0</span>],
            <span class="hljs-string">&#x27;total_volume&#x27;</span>: stats[<span class="hljs-number">1</span>] <span class="hljs-keyword">or</span> <span class="hljs-number">0</span>,
            <span class="hljs-string">&#x27;total_fees&#x27;</span>: stats[<span class="hljs-number">2</span>] <span class="hljs-keyword">or</span> <span class="hljs-number">0</span>,
            <span class="hljs-string">&#x27;avg_price&#x27;</span>: stats[<span class="hljs-number">3</span>] <span class="hljs-keyword">or</span> <span class="hljs-number">0</span>
        }
</code></pre>
<h3 id="健康检查和监控">健康检查和监控</h3>
<pre><code class="language-python"><span class="hljs-keyword">import</span> requests
<span class="hljs-keyword">import</span> time
<span class="hljs-keyword">from</span> threading <span class="hljs-keyword">import</span> Thread

<span class="hljs-keyword">class</span> <span class="hljs-title class_">HealthMonitor</span>:
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">__init__</span>(<span class="hljs-params">self, exchange, webhook_url=<span class="hljs-literal">None</span></span>):
        self.exchange = exchange
        self.webhook_url = webhook_url
        self.is_healthy = <span class="hljs-literal">True</span>
        self.last_check = time.time()

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">check_exchange_connection</span>(<span class="hljs-params">self</span>):
        <span class="hljs-string">&quot;&quot;&quot;检查交易所连接&quot;&quot;&quot;</span>
        <span class="hljs-keyword">try</span>:
            self.exchange.fetch_ticker(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>)
            <span class="hljs-keyword">return</span> <span class="hljs-literal">True</span>
        <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;交易所连接检查失败: <span class="hljs-subst">{e}</span>&quot;</span>)
            <span class="hljs-keyword">return</span> <span class="hljs-literal">False</span>

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">check_api_limits</span>(<span class="hljs-params">self</span>):
        <span class="hljs-string">&quot;&quot;&quot;检查API限制&quot;&quot;&quot;</span>
        <span class="hljs-keyword">try</span>:
            <span class="hljs-comment"># 检查剩余请求次数</span>
            response = self.exchange.fetch_ticker(<span class="hljs-string">&#x27;BTC/USDT&#x27;</span>)
            <span class="hljs-comment"># 某些交易所会在响应头中返回限制信息</span>
            <span class="hljs-keyword">return</span> <span class="hljs-literal">True</span>
        <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
            <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;API限制检查失败: <span class="hljs-subst">{e}</span>&quot;</span>)
            <span class="hljs-keyword">return</span> <span class="hljs-literal">False</span>

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">send_alert</span>(<span class="hljs-params">self, message</span>):
        <span class="hljs-string">&quot;&quot;&quot;发送告警&quot;&quot;&quot;</span>
        <span class="hljs-keyword">if</span> self.webhook_url:
            <span class="hljs-keyword">try</span>:
                payload = {
                    <span class="hljs-string">&#x27;text&#x27;</span>: <span class="hljs-string">f&quot;🚨 交易机器人告警: <span class="hljs-subst">{message}</span>&quot;</span>,
                    <span class="hljs-string">&#x27;timestamp&#x27;</span>: datetime.now().isoformat()
                }
                requests.post(self.webhook_url, json=payload)
            <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
                <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;发送告警失败: <span class="hljs-subst">{e}</span>&quot;</span>)

        <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;ALERT: <span class="hljs-subst">{message}</span>&quot;</span>)

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">health_check</span>(<span class="hljs-params">self</span>):
        <span class="hljs-string">&quot;&quot;&quot;执行健康检查&quot;&quot;&quot;</span>
        current_time = time.time()

        <span class="hljs-comment"># 检查交易所连接</span>
        <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> self.check_exchange_connection():
            <span class="hljs-keyword">if</span> self.is_healthy:
                self.send_alert(<span class="hljs-string">&quot;交易所连接失败&quot;</span>)
                self.is_healthy = <span class="hljs-literal">False</span>
        <span class="hljs-keyword">else</span>:
            <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> self.is_healthy:
                self.send_alert(<span class="hljs-string">&quot;交易所连接已恢复&quot;</span>)
                self.is_healthy = <span class="hljs-literal">True</span>

        self.last_check = current_time

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">start_monitoring</span>(<span class="hljs-params">self, interval=<span class="hljs-number">60</span></span>):
        <span class="hljs-string">&quot;&quot;&quot;启动监控线程&quot;&quot;&quot;</span>
        <span class="hljs-keyword">def</span> <span class="hljs-title function_">monitor</span>():
            <span class="hljs-keyword">while</span> <span class="hljs-literal">True</span>:
                self.health_check()
                time.sleep(interval)

        monitor_thread = Thread(target=monitor, daemon=<span class="hljs-literal">True</span>)
        monitor_thread.start()
        <span class="hljs-built_in">print</span>(<span class="hljs-string">&quot;健康监控已启动&quot;</span>)
</code></pre>
<h3 id="完整的生产级机器人框架">完整的生产级机器人框架</h3>
<pre><code class="language-python"><span class="hljs-keyword">import</span> ccxt
<span class="hljs-keyword">import</span> time
<span class="hljs-keyword">import</span> signal
<span class="hljs-keyword">import</span> sys
<span class="hljs-keyword">from</span> threading <span class="hljs-keyword">import</span> Thread, Event
<span class="hljs-keyword">from</span> datetime <span class="hljs-keyword">import</span> datetime

<span class="hljs-keyword">class</span> <span class="hljs-title class_">ProductionTradingBot</span>:
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">__init__</span>(<span class="hljs-params">self, config</span>):
        self.config = config
        self.exchange = self.init_exchange()
        self.db = DatabaseManager()
        self.logger = TradingLogger()
        self.monitor = HealthMonitor(self.exchange)
        self.running = Event()
        self.running.<span class="hljs-built_in">set</span>()

        <span class="hljs-comment"># 注册信号处理器</span>
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">init_exchange</span>(<span class="hljs-params">self</span>):
        <span class="hljs-string">&quot;&quot;&quot;初始化交易所&quot;&quot;&quot;</span>
        exchange_class = <span class="hljs-built_in">getattr</span>(ccxt, self.config.EXCHANGE)
        <span class="hljs-keyword">return</span> exchange_class({
            <span class="hljs-string">&#x27;apiKey&#x27;</span>: self.config.API_KEY,
            <span class="hljs-string">&#x27;secret&#x27;</span>: self.config.API_SECRET,
            <span class="hljs-string">&#x27;sandbox&#x27;</span>: self.config.SANDBOX,
            <span class="hljs-string">&#x27;enableRateLimit&#x27;</span>: <span class="hljs-literal">True</span>,
        })

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">signal_handler</span>(<span class="hljs-params">self, signum, frame</span>):
        <span class="hljs-string">&quot;&quot;&quot;信号处理器&quot;&quot;&quot;</span>
        <span class="hljs-built_in">print</span>(<span class="hljs-string">f&quot;收到信号 <span class="hljs-subst">{signum}</span>，正在优雅关闭...&quot;</span>)
        self.running.clear()

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">execute_strategy</span>(<span class="hljs-params">self</span>):
        <span class="hljs-string">&quot;&quot;&quot;执行交易策略&quot;&quot;&quot;</span>
        <span class="hljs-keyword">try</span>:
            <span class="hljs-comment"># 这里实现你的交易策略</span>
            <span class="hljs-keyword">for</span> symbol <span class="hljs-keyword">in</span> self.config.SYMBOLS:
                ticker = self.exchange.fetch_ticker(symbol)
                self.logger.logger.info(<span class="hljs-string">f&quot;<span class="hljs-subst">{symbol}</span> 当前价格: <span class="hljs-subst">{ticker[<span class="hljs-string">&#x27;last&#x27;</span>]}</span>&quot;</span>)

                <span class="hljs-comment"># 示例策略逻辑</span>
                <span class="hljs-comment"># if self.should_buy(symbol, ticker):</span>
                <span class="hljs-comment">#     self.place_buy_order(symbol)</span>
                <span class="hljs-comment"># elif self.should_sell(symbol, ticker):</span>
                <span class="hljs-comment">#     self.place_sell_order(symbol)</span>

        <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
            self.logger.log_error(e, <span class="hljs-string">&quot;执行策略时出错&quot;</span>)

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">save_balance_snapshot</span>(<span class="hljs-params">self</span>):
        <span class="hljs-string">&quot;&quot;&quot;保存余额快照&quot;&quot;&quot;</span>
        <span class="hljs-keyword">try</span>:
            balance = self.exchange.fetch_balance()
            <span class="hljs-comment"># 计算总价值（简化版本）</span>
            total_value = balance[<span class="hljs-string">&#x27;USDT&#x27;</span>][<span class="hljs-string">&#x27;total&#x27;</span>] <span class="hljs-keyword">if</span> <span class="hljs-string">&#x27;USDT&#x27;</span> <span class="hljs-keyword">in</span> balance <span class="hljs-keyword">else</span> <span class="hljs-number">0</span>

            self.db.save_balance_snapshot(balance, total_value)
            self.logger.log_balance(balance)
        <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
            self.logger.log_error(e, <span class="hljs-string">&quot;保存余额快照时出错&quot;</span>)

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">run</span>(<span class="hljs-params">self</span>):
        <span class="hljs-string">&quot;&quot;&quot;主运行循环&quot;&quot;&quot;</span>
        <span class="hljs-built_in">print</span>(<span class="hljs-string">&quot;交易机器人启动中...&quot;</span>)

        <span class="hljs-comment"># 启动健康监控</span>
        self.monitor.start_monitoring()

        <span class="hljs-comment"># 启动余额快照线程</span>
        <span class="hljs-keyword">def</span> <span class="hljs-title function_">balance_snapshot_worker</span>():
            <span class="hljs-keyword">while</span> self.running.is_set():
                self.save_balance_snapshot()
                time.sleep(<span class="hljs-number">3600</span>)  <span class="hljs-comment"># 每小时保存一次</span>

        balance_thread = Thread(target=balance_snapshot_worker, daemon=<span class="hljs-literal">True</span>)
        balance_thread.start()

        <span class="hljs-built_in">print</span>(<span class="hljs-string">&quot;交易机器人已启动&quot;</span>)

        <span class="hljs-comment"># 主循环</span>
        <span class="hljs-keyword">while</span> self.running.is_set():
            <span class="hljs-keyword">try</span>:
                self.execute_strategy()
                time.sleep(<span class="hljs-number">30</span>)  <span class="hljs-comment"># 30秒执行一次</span>
            <span class="hljs-keyword">except</span> KeyboardInterrupt:
                <span class="hljs-keyword">break</span>
            <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
                self.logger.log_error(e, <span class="hljs-string">&quot;主循环出错&quot;</span>)
                time.sleep(<span class="hljs-number">60</span>)  <span class="hljs-comment"># 出错后等待1分钟</span>

        <span class="hljs-built_in">print</span>(<span class="hljs-string">&quot;交易机器人已停止&quot;</span>)

<span class="hljs-comment"># 使用示例</span>
<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">&quot;__main__&quot;</span>:
    config = Config()
    bot = ProductionTradingBot(config)
    bot.run()
</code></pre>
<h3 id="部署脚本">部署脚本</h3>
<p><a href="http://deploy.sh">deploy.sh</a>：</p>
<pre><code class="language-bash"><span class="hljs-meta">#!/bin/bash</span>

<span class="hljs-comment"># 构建Docker镜像</span>
docker build -t trading-bot:latest .

<span class="hljs-comment"># 停止旧容器</span>
docker-compose down

<span class="hljs-comment"># 启动新容器</span>
docker-compose up -d

<span class="hljs-comment"># 检查容器状态</span>
docker-compose ps

<span class="hljs-comment"># 查看日志</span>
docker-compose logs -f trading-bot
</code></pre>
<h3 id="监控脚本">监控脚本</h3>
<p><a href="http://monitor.py">monitor.py</a>：</p>
<pre><code class="language-python"><span class="hljs-comment">#!/usr/bin/env python3</span>
<span class="hljs-keyword">import</span> subprocess
<span class="hljs-keyword">import</span> time
<span class="hljs-keyword">import</span> requests

<span class="hljs-keyword">def</span> <span class="hljs-title function_">check_container_health</span>():
    <span class="hljs-string">&quot;&quot;&quot;检查容器健康状态&quot;&quot;&quot;</span>
    <span class="hljs-keyword">try</span>:
        result = subprocess.run(
            [<span class="hljs-string">&#x27;docker-compose&#x27;</span>, <span class="hljs-string">&#x27;ps&#x27;</span>, <span class="hljs-string">&#x27;-q&#x27;</span>, <span class="hljs-string">&#x27;trading-bot&#x27;</span>],
            capture_output=<span class="hljs-literal">True</span>, text=<span class="hljs-literal">True</span>
        )
        <span class="hljs-keyword">return</span> <span class="hljs-built_in">bool</span>(result.stdout.strip())
    <span class="hljs-keyword">except</span>:
        <span class="hljs-keyword">return</span> <span class="hljs-literal">False</span>

<span class="hljs-keyword">def</span> <span class="hljs-title function_">restart_container</span>():
    <span class="hljs-string">&quot;&quot;&quot;重启容器&quot;&quot;&quot;</span>
    <span class="hljs-keyword">try</span>:
        subprocess.run([<span class="hljs-string">&#x27;docker-compose&#x27;</span>, <span class="hljs-string">&#x27;restart&#x27;</span>, <span class="hljs-string">&#x27;trading-bot&#x27;</span>])
        <span class="hljs-keyword">return</span> <span class="hljs-literal">True</span>
    <span class="hljs-keyword">except</span>:
        <span class="hljs-keyword">return</span> <span class="hljs-literal">False</span>

<span class="hljs-keyword">def</span> <span class="hljs-title function_">send_notification</span>(<span class="hljs-params">message</span>):
    <span class="hljs-string">&quot;&quot;&quot;发送通知&quot;&quot;&quot;</span>
    webhook_url = <span class="hljs-string">&quot;YOUR_WEBHOOK_URL&quot;</span>
    <span class="hljs-keyword">if</span> webhook_url:
        <span class="hljs-keyword">try</span>:
            requests.post(webhook_url, json={<span class="hljs-string">&#x27;text&#x27;</span>: message})
        <span class="hljs-keyword">except</span>:
            <span class="hljs-keyword">pass</span>
    <span class="hljs-built_in">print</span>(message)

<span class="hljs-keyword">def</span> <span class="hljs-title function_">main</span>():
    <span class="hljs-keyword">while</span> <span class="hljs-literal">True</span>:
        <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> check_container_health():
            send_notification(<span class="hljs-string">&quot;🚨 交易机器人容器已停止，正在重启...&quot;</span>)
            <span class="hljs-keyword">if</span> restart_container():
                send_notification(<span class="hljs-string">&quot;✅ 交易机器人容器已重启&quot;</span>)
            <span class="hljs-keyword">else</span>:
                send_notification(<span class="hljs-string">&quot;❌ 交易机器人容器重启失败&quot;</span>)

        time.sleep(<span class="hljs-number">60</span>)  <span class="hljs-comment"># 每分钟检查一次</span>

<span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">&quot;__main__&quot;</span>:
    main()
</code></pre>
<h2 id="总结">总结</h2>
<p>CCXT是一个功能强大的加密货币交易库，支持多种编程语言和众多交易所。本文档涵盖了从基础使用到高级功能的各个方面：</p>
<h3 id="主要特性">主要特性</h3>
<ul>
<li><strong>多语言支持</strong>：JavaScript、Python、PHP、C#、Go</li>
<li><strong>统一API</strong>：标准化的接口，便于跨交易所开发</li>
<li><strong>丰富功能</strong>：支持现货、期货、期权等多种交易类型</li>
<li><strong>实时数据</strong>：通过CCXT Pro支持WebSocket数据流</li>
<li><strong>生产就绪</strong>：完善的错误处理和频率限制机制</li>
</ul>
<h3 id="适用场景">适用场景</h3>
<ul>
<li>算法交易和量化策略</li>
<li>套利机器人开发</li>
<li>市场数据分析</li>
<li>投资组合管理</li>
<li>交易所API集成</li>
</ul>
<h3 id="最佳实践">最佳实践</h3>
<ol>
<li><strong>安全第一</strong>：妥善保管API密钥，使用环境变量</li>
<li><strong>风险控制</strong>：实施止损止盈和仓位管理</li>
<li><strong>错误处理</strong>：完善的异常处理和重试机制</li>
<li><strong>监控日志</strong>：详细的日志记录和性能监控</li>
<li><strong>测试验证</strong>：先在测试环境验证策略</li>
</ol>
<h3 id="注意事项">注意事项</h3>
<ul>
<li>交易有风险，投资需谨慎</li>
<li>充分测试后再投入实盘</li>
<li>关注交易所的API变更和维护公告</li>
<li>遵守各交易所的使用条款和频率限制</li>
</ul>
<p>希望这份中文文档能帮助您更好地使用CCXT库进行加密货币交易开发！</p>
<hr>
<p><em>本文档基于CCXT v4.4.98版本编写，最后更新时间：2025年8月</em></p>

            <script async src="https://cdn.jsdelivr.net/npm/katex-copytex@latest/dist/katex-copytex.min.js"></script>
            
        </body>
        </html>